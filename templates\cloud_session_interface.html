<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Comment Deleter - Cloud Platform</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .step.active {
            border-left-color: #28a745;
        }
        
        .step h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .step p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .cookie-upload {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .cookie-upload h4 {
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .cookie-upload textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            resize: vertical;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .instructions h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #856404;
        }
        
        .comments-section {
            margin-top: 30px;
            display: none;
        }
        
        .comment-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .comment-content {
            flex: 1;
        }
        
        .comment-date {
            color: #666;
            font-size: 14px;
        }
        
        .comment-text {
            margin-top: 5px;
            color: #333;
        }
        
        .comment-video {
            margin-top: 3px;
            color: #007bff;
            font-size: 14px;
        }
        
        .progress-section {
            background: #e7f3ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: #856404;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🌐 YouTube Comment Deleter</h1>
            <p>Cloud Platform - Session Cookie Authentication</p>
            <span id="connectionStatus" class="status-indicator status-disconnected">Disconnected</span>
        </header>

        <div class="content">
            <div class="warning">
                <h3>⚠️ Important Warning</h3>
                <p><strong>This will permanently delete ALL your YouTube comments.</strong></p>
                <p>This action cannot be undone!</p>
            </div>

            <div class="step active" id="step1">
                <h3>Step 1: Get Your Session Cookies</h3>
                <p>To access your YouTube comments, we need your browser session cookies.</p>
                
                <div class="instructions">
                    <h4>How to get your cookies:</h4>
                    <ol>
                        <li>Open a new tab and go to: <strong>myactivity.google.com</strong></li>
                        <li>Sign in to your Google account</li>
                        <li>Press <strong>F12</strong> to open Developer Tools</li>
                        <li>Go to <strong>Application</strong> tab → <strong>Cookies</strong> → <strong>https://myactivity.google.com</strong></li>
                        <li>Copy all cookies (or use the export method below)</li>
                    </ol>
                </div>

                <div class="cookie-upload">
                    <h4>Paste your cookies here:</h4>
                    <textarea id="cookiesInput" placeholder="Paste your session cookies here...&#10;&#10;Format: name1=value1; name2=value2; name3=value3&#10;&#10;Or paste JSON format from browser extension"></textarea>
                    <br><br>
                    <button id="uploadCookiesBtn" class="btn btn-primary">Upload Cookies</button>
                </div>
            </div>

            <div class="step" id="step2" style="display: none;">
                <h3>Step 2: Load Your Comments</h3>
                <p>Your session cookies have been uploaded. Now load your YouTube comments.</p>
                <button id="loadCommentsBtn" class="btn btn-primary">📋 Load Comments</button>
            </div>

            <div class="step" id="step3" style="display: none;">
                <h3>Step 3: Delete Comments</h3>
                <p>Review your comments below and delete them.</p>
                <button id="deleteAllBtn" class="btn btn-danger">🗑️ Delete All Comments</button>
            </div>

            <div class="progress-section" id="progressSection">
                <h4>Deletion Progress</h4>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText">Ready to start...</div>
            </div>

            <div class="comments-section" id="commentsSection">
                <h3>Your YouTube Comments</h3>
                <div id="commentsList">
                    <!-- Comments will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        
        const uploadCookiesBtn = document.getElementById('uploadCookiesBtn');
        const loadCommentsBtn = document.getElementById('loadCommentsBtn');
        const deleteAllBtn = document.getElementById('deleteAllBtn');
        const cookiesInput = document.getElementById('cookiesInput');
        const commentsList = document.getElementById('commentsList');
        const commentsSection = document.getElementById('commentsSection');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // Event listeners
        uploadCookiesBtn.addEventListener('click', uploadCookies);
        loadCommentsBtn.addEventListener('click', loadComments);
        deleteAllBtn.addEventListener('click', deleteAllComments);
        
        // Socket events
        socket.on('connect', () => {
            console.log('Connected to cloud server');
            connectionStatus.textContent = 'Connected';
            connectionStatus.className = 'status-indicator status-connected';
        });
        
        socket.on('disconnect', () => {
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.className = 'status-indicator status-disconnected';
        });
        
        socket.on('comments_loaded', (data) => {
            displayComments(data.comments || []);
            loadCommentsBtn.textContent = 'Comments Loaded ✓';
            showStep(3);
            commentsSection.style.display = 'block';
        });
        
        socket.on('comments_error', (data) => {
            alert(`Error loading comments: ${data.error}`);
            loadCommentsBtn.textContent = 'Load Comments';
            loadCommentsBtn.disabled = false;
        });
        
        socket.on('deletion_progress', (data) => {
            progressSection.style.display = 'block';
            const percentage = (data.deleted / data.total) * 100;
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = data.message;
        });
        
        socket.on('deletion_complete', (data) => {
            progressFill.style.width = '100%';
            progressText.textContent = data.message;
            deleteAllBtn.textContent = 'Deletion Complete ✓';
            deleteAllBtn.disabled = true;
        });
        
        socket.on('deletion_error', (data) => {
            alert(`Deletion error: ${data.error}`);
        });
        
        socket.on('delete_success', (data) => {
            const commentElement = document.querySelector(`[data-comment-id="${data.comment_id}"]`);
            if (commentElement) {
                commentElement.remove();
            }
        });
        
        // Functions
        function uploadCookies() {
            const cookiesText = cookiesInput.value.trim();
            
            if (!cookiesText) {
                alert('Please paste your cookies first');
                return;
            }
            
            uploadCookiesBtn.textContent = 'Uploading...';
            uploadCookiesBtn.disabled = true;
            
            // Try to parse cookies
            let cookies;
            try {
                // Try JSON format first
                cookies = JSON.parse(cookiesText);
            } catch (e) {
                // Fallback to string format
                cookies = cookiesText;
            }
            
            fetch('/upload_cookies', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cookies: cookies })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    uploadCookiesBtn.textContent = 'Cookies Uploaded ✓';
                    showStep(2);
                } else {
                    alert(`Error: ${data.error}`);
                    uploadCookiesBtn.textContent = 'Upload Cookies';
                    uploadCookiesBtn.disabled = false;
                }
            })
            .catch(error => {
                alert(`Upload failed: ${error}`);
                uploadCookiesBtn.textContent = 'Upload Cookies';
                uploadCookiesBtn.disabled = false;
            });
        }
        
        function loadComments() {
            loadCommentsBtn.textContent = 'Loading...';
            loadCommentsBtn.disabled = true;
            socket.emit('load_comments');
        }
        
        function deleteAllComments() {
            if (confirm('Are you absolutely sure you want to delete ALL your YouTube comments? This cannot be undone!')) {
                deleteAllBtn.textContent = 'Deleting...';
                deleteAllBtn.disabled = true;
                socket.emit('delete_all_comments');
            }
        }
        
        function deleteComment(commentId, deleteInfo) {
            if (confirm('Delete this comment?')) {
                socket.emit('delete_comment', { 
                    comment_id: commentId, 
                    delete_info: deleteInfo 
                });
            }
        }
        
        function displayComments(comments) {
            if (comments.length === 0) {
                commentsList.innerHTML = '<p>No YouTube comments found.</p>';
                return;
            }
            
            let html = `<h4>Found ${comments.length} comments:</h4>`;
            
            comments.forEach(comment => {
                html += `
                    <div class="comment-item" data-comment-id="${comment.id}">
                        <div class="comment-content">
                            <div class="comment-date">${comment.date}</div>
                            <div class="comment-text">${comment.text}</div>
                            <div class="comment-video">Video: ${comment.video_title}</div>
                        </div>
                        <button class="btn btn-danger btn-sm" onclick="deleteComment('${comment.id}', '${comment.delete_url}')">Delete</button>
                    </div>
                `;
            });
            
            commentsList.innerHTML = html;
        }
        
        function showStep(stepNumber) {
            document.getElementById(`step${stepNumber}`).style.display = 'block';
            document.getElementById(`step${stepNumber}`).classList.add('active');
        }
    </script>
</body>
</html>
