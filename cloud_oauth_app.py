from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import requests
import json
import os
from datetime import datetime, timedelta
import secrets

app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
socketio = SocketIO(app, cors_allowed_origins="*")

# Google OAuth Configuration
GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID', 'your-client-id')
GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET', 'your-client-secret')
GOOGLE_REDIRECT_URI = os.environ.get('GOOGLE_REDIRECT_URI', 'http://localhost:5000/oauth/callback')

# Google OAuth URLs
GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"

# Required scopes for accessing My Activity
SCOPES = [
    'openid',
    'email',
    'profile',
    'https://www.googleapis.com/auth/activity'  # Access to My Activity data
]

class CloudCommentManager:
    def __init__(self):
        self.access_token = None
        self.user_info = None
    
    def set_access_token(self, token):
        """Set the OAuth access token"""
        self.access_token = token
    
    def get_user_activities(self):
        """Fetch user's YouTube activities from Google My Activity API"""
        if not self.access_token:
            return {"error": "No access token"}
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        # Note: Google My Activity API is limited - this is a conceptual implementation
        # In practice, you might need to use web scraping with authenticated requests
        try:
            # This would be the actual API call if Google provided a public My Activity API
            url = "https://myactivity.google.com/api/activities"
            params = {
                'product': 'YouTube',
                'activity_type': 'comment'
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API call failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Failed to fetch activities: {str(e)}"}
    
    def delete_activity(self, activity_id):
        """Delete a specific activity"""
        if not self.access_token:
            return {"error": "No access token"}
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            url = f"https://myactivity.google.com/api/activities/{activity_id}"
            response = requests.delete(url, headers=headers)
            
            if response.status_code == 200:
                return {"success": True}
            else:
                return {"error": f"Delete failed: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Failed to delete activity: {str(e)}"}

# Global comment manager
comment_manager = CloudCommentManager()

@app.route('/')
def index():
    """Main page - check if user is authenticated"""
    if 'access_token' in session:
        return render_template('cloud_oauth_interface.html', authenticated=True)
    else:
        return render_template('cloud_oauth_interface.html', authenticated=False)

@app.route('/login')
def login():
    """Initiate OAuth login flow"""
    # Generate state parameter for security
    state = secrets.token_urlsafe(32)
    session['oauth_state'] = state
    
    # Build OAuth URL
    params = {
        'client_id': GOOGLE_CLIENT_ID,
        'redirect_uri': GOOGLE_REDIRECT_URI,
        'scope': ' '.join(SCOPES),
        'response_type': 'code',
        'state': state,
        'access_type': 'offline',
        'prompt': 'consent'
    }
    
    auth_url = GOOGLE_AUTH_URL + '?' + '&'.join([f'{k}={v}' for k, v in params.items()])
    return redirect(auth_url)

@app.route('/oauth/callback')
def oauth_callback():
    """Handle OAuth callback"""
    # Verify state parameter
    if request.args.get('state') != session.get('oauth_state'):
        return jsonify({'error': 'Invalid state parameter'}), 400
    
    # Get authorization code
    code = request.args.get('code')
    if not code:
        return jsonify({'error': 'No authorization code received'}), 400
    
    # Exchange code for tokens
    token_data = {
        'client_id': GOOGLE_CLIENT_ID,
        'client_secret': GOOGLE_CLIENT_SECRET,
        'code': code,
        'grant_type': 'authorization_code',
        'redirect_uri': GOOGLE_REDIRECT_URI
    }
    
    try:
        response = requests.post(GOOGLE_TOKEN_URL, data=token_data)
        tokens = response.json()
        
        if 'access_token' in tokens:
            # Store tokens in session
            session['access_token'] = tokens['access_token']
            session['refresh_token'] = tokens.get('refresh_token')
            
            # Set up comment manager
            comment_manager.set_access_token(tokens['access_token'])
            
            # Get user info
            headers = {'Authorization': f'Bearer {tokens["access_token"]}'}
            user_response = requests.get(GOOGLE_USERINFO_URL, headers=headers)
            session['user_info'] = user_response.json()
            
            return redirect(url_for('index'))
        else:
            return jsonify({'error': 'Failed to get access token'}), 400
            
    except Exception as e:
        return jsonify({'error': f'OAuth callback failed: {str(e)}'}), 500

@app.route('/logout')
def logout():
    """Logout user"""
    session.clear()
    return redirect(url_for('index'))

@socketio.on('connect')
def handle_connect():
    print('Client connected to cloud OAuth interface')
    emit('status', {'message': 'Connected to cloud server'})

@socketio.on('get_activities')
def handle_get_activities():
    """Fetch user's YouTube comment activities"""
    if 'access_token' not in session:
        emit('activities_error', {'error': 'Not authenticated'})
        return
    
    comment_manager.set_access_token(session['access_token'])
    activities = comment_manager.get_user_activities()
    
    if 'error' in activities:
        emit('activities_error', activities)
    else:
        emit('activities_loaded', activities)

@socketio.on('delete_activity')
def handle_delete_activity(data):
    """Delete a specific activity"""
    if 'access_token' not in session:
        emit('delete_error', {'error': 'Not authenticated'})
        return
    
    activity_id = data.get('activity_id')
    if not activity_id:
        emit('delete_error', {'error': 'No activity ID provided'})
        return
    
    comment_manager.set_access_token(session['access_token'])
    result = comment_manager.delete_activity(activity_id)
    
    if 'error' in result:
        emit('delete_error', result)
    else:
        emit('delete_success', {'activity_id': activity_id})

@socketio.on('delete_all_comments')
def handle_delete_all_comments():
    """Delete all YouTube comments"""
    if 'access_token' not in session:
        emit('deletion_error', {'error': 'Not authenticated'})
        return
    
    comment_manager.set_access_token(session['access_token'])
    
    # Get all activities first
    activities = comment_manager.get_user_activities()
    
    if 'error' in activities:
        emit('deletion_error', activities)
        return
    
    # Delete each activity
    deleted_count = 0
    total_count = len(activities.get('items', []))
    
    for activity in activities.get('items', []):
        result = comment_manager.delete_activity(activity['id'])
        if 'error' not in result:
            deleted_count += 1
            emit('deletion_progress', {
                'deleted': deleted_count,
                'total': total_count,
                'message': f'Deleted {deleted_count} of {total_count} comments'
            })
    
    emit('deletion_complete', {
        'deleted': deleted_count,
        'total': total_count,
        'message': f'Deletion complete! Deleted {deleted_count} comments.'
    })

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'version': 'cloud_oauth',
        'authenticated': 'access_token' in session
    })

if __name__ == '__main__':
    print("Starting YouTube Comment Deleter - Cloud OAuth Edition...")
    print("Configure your Google OAuth credentials:")
    print("1. Go to Google Cloud Console")
    print("2. Create OAuth 2.0 credentials")
    print("3. Set environment variables:")
    print("   GOOGLE_CLIENT_ID=your-client-id")
    print("   GOOGLE_CLIENT_SECRET=your-client-secret")
    print("   GOOGLE_REDIRECT_URI=http://localhost:5000/oauth/callback")
    print("")
    print("Access the application at: http://localhost:5000")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
