import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import threading
from concurrent.futures import ThreadPoolExecutor
import base64
import io
from PIL import Image

class UltraFastCommentDeleter:
    def __init__(self, progress_callback=None, refresh_interval=10, max_stuck_time=8):
        self.driver = None
        self.progress_callback = progress_callback
        self.deleted_count = 0
        self.target_speed = 4  # 4 deletes per second
        self.delete_interval = 1.0 / self.target_speed  # 0.25 seconds between deletes

        # Auto-refresh settings - configurable by user
        self.refresh_interval = refresh_interval  # User configurable refresh interval
        self.max_stuck_time = max_stuck_time     # User configurable stuck detection
        self.last_progress_time = time.time()
        self.last_deleted_count = 0

        # Performance tracking for smart refresh
        self.deletion_times = []
        self.last_deletion_time = time.time()
        self.performance_refresh_threshold = 25  # Refresh every 25 deletions
        self.speed_degradation_threshold = 1.5   # Refresh if speed drops 50%

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Browser interaction settings
        self.screenshot_enabled = False
        self.last_screenshot = None
        self.original_width = 1200
        self.original_height = 800
        self.display_width = 1000
        self.display_height = 700

    def update_refresh_settings(self, refresh_interval=None, max_stuck_time=None):
        """Update refresh settings during runtime"""
        if refresh_interval is not None:
            self.refresh_interval = refresh_interval
            self._update_progress(f"🔄 Refresh interval updated to {refresh_interval}s")

        if max_stuck_time is not None:
            self.max_stuck_time = max_stuck_time
            self._update_progress(f"⏱️ Stuck detection updated to {max_stuck_time}s")

    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver optimized for interaction"""
        try:
            chrome_options = ChromeOptions()

            # Basic optimizations (keep JavaScript enabled for interaction)
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            # Removed --disable-javascript to allow interactions
            # Removed --disable-images to see what we're clicking

            # Interaction-friendly settings
            chrome_options.add_argument("--enable-automation")  # Allow automation
            chrome_options.add_argument("--disable-web-security")  # Allow cross-origin
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-iframes-sandbox")

            # Anti-detection (minimal)
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Add user agent to appear more normal
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            if headless:
                chrome_options.add_argument("--headless")

            # Get ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set reasonable timeouts for interaction
            self.driver.implicitly_wait(2)  # Longer wait for elements
            self.driver.set_page_load_timeout(30)  # Longer page load timeout

            # Execute script to remove webdriver detection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self._update_progress("Interactive browser started!")
            return True

        except Exception as e:
            self._update_progress(f"Failed to start browser: {str(e)}")
            return False

    def go_to_activity_page(self):
        """Navigate to YouTube comments page"""
        try:
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self._update_progress("Navigating to YouTube Comments page...")

            self.driver.get(url)

            # Quick wait for page load
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            self._update_progress("Comments page loaded. Please sign in if needed.")
            return True

        except Exception as e:
            self._update_progress(f"Failed to navigate: {str(e)}")
            return False

    def wait_for_login(self):
        """Quick login check"""
        self._update_progress("Checking login status...")

        try:
            # Quick check for logged-in state
            WebDriverWait(self.driver, 30).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("Login confirmed! Ready for ultra-fast deletion.")
            return True
        except TimeoutException:
            self._update_progress("Login timeout.")
            return False

    def _is_logged_in(self):
        """Quick login check"""
        try:
            # Look for any activity content
            indicators = self.driver.find_elements(By.CSS_SELECTOR, "div[data-ved], c-wiz")
            return len(indicators) > 0
        except:
            return False

    def ultra_fast_delete_all(self):
        """Ultra-fast deletion using JavaScript and optimized strategies with auto-refresh"""
        try:
            self._update_progress("🚀 Starting ULTRA-FAST deletion (4 deletes/second)...")
            self._update_progress(f"🔄 Auto-refresh enabled: every {self.refresh_interval}s or when stuck for {self.max_stuck_time}s")

            start_time = time.time()
            self.last_progress_time = start_time

            while True:
                cycle_start = time.time()
                initial_count = self.deleted_count

                # Method 1: Pure JavaScript batch deletion (fastest)
                deleted_js = self._javascript_batch_delete()

                if deleted_js > 0:
                    self._update_progress(f"JavaScript batch deleted {deleted_js} comments!")
                    self.deleted_count += deleted_js
                    self._update_progress_tracking()

                # Method 2: Optimized Selenium for remaining comments
                selenium_deleted = self._optimized_selenium_delete()

                # Check if we made progress this cycle
                cycle_deleted = self.deleted_count - initial_count

                if cycle_deleted == 0:
                    # No progress made, check if we need to refresh
                    if self._should_refresh():
                        self._refresh_page()
                        continue
                    else:
                        # No comments found and no need to refresh - we're done
                        self._update_progress("🔍 No more comments found - deletion process complete!")
                        break
                else:
                    self._update_progress_tracking()

                # Check for time-based refresh
                if time.time() - cycle_start > self.refresh_interval:
                    self._update_progress(f"⏰ {self.refresh_interval}s interval reached - refreshing page")
                    self._refresh_page()

            # Send completion status
            completion_message = f"🎉 Ultra-fast deletion complete! Total: {self.deleted_count}"
            self._update_progress(completion_message)

            # Send specific completion signal
            if self.progress_callback:
                self.progress_callback({
                    'type': 'completion',
                    'message': completion_message,
                    'total_deleted': self.deleted_count,
                    'status': 'complete'
                })

        except Exception as e:
            error_message = f"Error during ultra-fast deletion: {str(e)}"
            self._update_progress(error_message)

            # Send specific error signal
            if self.progress_callback:
                self.progress_callback({
                    'type': 'error',
                    'message': error_message,
                    'status': 'error'
                })

    def _should_refresh(self):
        """Check if page should be refreshed due to being stuck OR performance degradation"""
        current_time = time.time()
        time_since_progress = current_time - self.last_progress_time

        # Check for stuck state (no progress)
        if time_since_progress > self.max_stuck_time:
            self._update_progress(f"⚠️ No progress for {time_since_progress:.1f}s - page may be stuck")
            return True

        # Check for performance degradation
        if self._should_refresh_for_performance():
            return True

        return False

    def _should_refresh_for_performance(self):
        """Check if we need to refresh due to performance degradation"""

        # Refresh after every N deletions to prevent performance degradation
        if self.deleted_count > 0 and self.deleted_count % self.performance_refresh_threshold == 0:
            self._update_progress(f"🔄 Performance refresh after {self.deleted_count} deletions")
            return True

        # Check if deletion speed has degraded significantly
        if len(self.deletion_times) >= 10:
            recent_avg = sum(self.deletion_times[-5:]) / 5  # Last 5 deletions
            initial_avg = sum(self.deletion_times[:5]) / 5   # First 5 deletions

            if recent_avg > initial_avg * self.speed_degradation_threshold:
                slowdown_percent = ((recent_avg - initial_avg) / initial_avg) * 100
                self._update_progress(f"📉 Speed degraded by {slowdown_percent:.1f}% - refreshing for performance")
                return True

        return False

    def _track_deletion_performance(self):
        """Track deletion performance for smart refresh decisions"""
        current_time = time.time()
        deletion_time = current_time - self.last_deletion_time

        self.deletion_times.append(deletion_time)

        # Keep only last 50 deletion times to prevent memory bloat
        if len(self.deletion_times) > 50:
            self.deletion_times = self.deletion_times[-50:]

        self.last_deletion_time = current_time

        # Log performance stats occasionally
        if self.deleted_count % 10 == 0 and len(self.deletion_times) >= 5:
            recent_avg = sum(self.deletion_times[-5:]) / 5
            self._update_progress(f"📊 Recent deletion speed: {1/recent_avg:.1f} deletes/second")

    def _update_progress_tracking(self):
        """Update progress tracking variables"""
        self.last_progress_time = time.time()
        self.last_deleted_count = self.deleted_count

    def _refresh_page(self):
        """Performance-focused refresh that mimics manual refresh behavior"""
        try:
            self._update_progress("🔄 Performance refresh (manual-like)...")

            # Store current URL
            current_url = self.driver.current_url

            # Clear browser state like manual refresh does
            try:
                self.driver.execute_script("window.localStorage.clear();")
                self.driver.execute_script("window.sessionStorage.clear();")
            except:
                pass

            # Hard refresh with cache clear (like Ctrl+F5)
            self.driver.execute_script("location.reload(true);")

            # Wait for complete page load (like manual observation)
            WebDriverWait(self.driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Wait for comment elements to be fully loaded and interactive
            try:
                WebDriverWait(self.driver, 8).until(
                    lambda driver: len(driver.find_elements(By.CSS_SELECTOR, "button")) > 5
                )
            except:
                # Fallback wait
                time.sleep(3)

            # Additional wait for JavaScript to settle (like manual observation)
            time.sleep(2)

            # Verify we're still on the right page
            if "myactivity.google.com" not in self.driver.current_url:
                self._update_progress("⚠️ Page changed, navigating back...")
                self.driver.get(current_url)
                time.sleep(2)

            # Reset performance tracking after refresh
            self.deletion_times = []  # Clear performance history
            self.last_deletion_time = time.time()

            self._update_progress("✅ Performance refresh complete - speed should be restored")
            self._update_progress_tracking()  # Reset stuck timer

        except Exception as e:
            self._update_progress(f"❌ Refresh error: {str(e)}")
            time.sleep(2)

    def _javascript_batch_delete(self):
        """Use JavaScript for maximum speed deletion"""
        self._update_progress("🔥 Attempting JavaScript batch deletion...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        js_script = f"""
        // Ultra-fast JavaScript deletion
        console.log('Starting ultra-fast deletion...');

        // Hamburger menu path to avoid
        const hamburgerMenuPath = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z";

        // Find all X buttons with the specific path, avoiding hamburger menu
        const selectors = [
            'svg.TjcpUd path[d="{x_button_path}"]',
            'svg.NMm5M path[d="{x_button_path}"]',
            'path[d="{x_button_path}"]',
            'svg[width="24"][height="24"] path[d*="19 6.41"]'
        ];

        let allXButtons = [];
        selectors.forEach(selector => {{
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(path => {{
                // Skip if this is the hamburger menu
                if (path.getAttribute('d') === hamburgerMenuPath) {{
                    return;
                }}

                const button = path.closest('button');
                if (button && !allXButtons.includes(button)) {{
                    // Double-check the button doesn't contain hamburger menu
                    const hamburgerPaths = button.querySelectorAll(`path[d="${{hamburgerMenuPath}}"]`);
                    if (hamburgerPaths.length === 0) {{
                        allXButtons.push(button);
                    }}
                }}
            }});
        }});

        console.log('Found', allXButtons.length, 'delete buttons (excluding hamburger menus)');

        // Delete at 4 per second (250ms intervals)
        let deleted = 0;
        const deleteInterval = 250; // 250ms = 4 per second

        allXButtons.forEach((button, index) => {{
            setTimeout(() => {{
                try {{
                    // Click the delete button
                    button.click();
                    deleted++;
                    console.log('Deleted comment', deleted);

                    // Try to auto-confirm if confirmation dialog appears
                    setTimeout(() => {{
                        const confirmButtons = document.querySelectorAll('button');
                        confirmButtons.forEach(btn => {{
                            const text = btn.textContent.toLowerCase();
                            if (text.includes('delete') || text.includes('remove') || text.includes('confirm')) {{
                                btn.click();
                            }}
                        }});
                    }}, 50);

                }} catch (e) {{
                    console.error('Error deleting comment', index, e);
                }}
            }}, index * deleteInterval);
        }});

        // Return the number of buttons found
        return allXButtons.length;
        """

        try:
            result = self.driver.execute_script(js_script)
            return result or 0
        except Exception as e:
            self._update_progress(f"JavaScript deletion failed: {str(e)}")
            return 0

    def _optimized_selenium_delete(self):
        """Optimized Selenium deletion for remaining comments"""
        self._update_progress("🔧 Running optimized Selenium cleanup...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        # Hamburger menu path to avoid
        hamburger_menu_path = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"

        # Ultra-fast selectors (most specific first), avoiding hamburger menu
        fast_selectors = [
            (By.CSS_SELECTOR, f'svg.TjcpUd path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, f'svg.NMm5M path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, 'svg[width="24"] path[d*="19 6.41"]'),
        ]

        start_time = time.time()
        deleted_this_round = 0

        while True:
            found_any = False
            round_start_count = self.deleted_count

            for by_method, selector in fast_selectors:
                try:
                    # Find elements quickly
                    elements = self.driver.find_elements(by_method, selector)

                    if elements:
                        found_any = True

                        # Process in batches for speed
                        batch_size = min(4, len(elements))  # 4 at a time for 4/second
                        batch = elements[:batch_size]

                        for path_element in batch:
                            try:
                                # Check if this path is the hamburger menu
                                path_d = path_element.get_attribute('d')
                                if path_d == hamburger_menu_path:
                                    continue  # Skip hamburger menu

                                # Find parent button quickly
                                button = path_element.find_element(By.XPATH, "./ancestor::button[1]")

                                # Double-check button doesn't contain hamburger menu
                                hamburger_paths = button.find_elements(By.CSS_SELECTOR, f'path[d="{hamburger_menu_path}"]')
                                if hamburger_paths:
                                    continue  # Skip if button contains hamburger menu

                                # Ultra-fast click
                                self.driver.execute_script("arguments[0].click();", button)

                                # Quick confirmation attempt
                                try:
                                    confirm_btn = WebDriverWait(self.driver, 0.1).until(
                                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Delete')]"))
                                    )
                                    self.driver.execute_script("arguments[0].click();", confirm_btn)
                                except TimeoutException:
                                    pass  # No confirmation needed

                                self.deleted_count += 1
                                deleted_this_round += 1
                                self._update_progress(f"⚡ Fast deleted #{self.deleted_count}")
                                self._update_progress_tracking()  # Update progress tracking
                                self._track_deletion_performance()  # Track performance for smart refresh

                                # Maintain 4/second speed
                                time.sleep(self.delete_interval)

                            except Exception as e:
                                continue

                        break  # Found elements with this selector, don't try others

                except Exception as e:
                    continue

            if not found_any:
                break

            # Check if we made progress this round
            round_deleted = self.deleted_count - round_start_count
            if round_deleted == 0:
                # No progress in this round, might be stuck
                break

            # Quick scroll to load more
            self.driver.execute_script("window.scrollBy(0, 1000);")
            time.sleep(0.5)  # Brief pause for loading

            # Safety timeout (don't run forever)
            if time.time() - start_time > 300:  # 5 minutes max
                self._update_progress("⏰ Safety timeout reached")
                break

        return deleted_this_round

    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)

        # Check for completion keywords and send explicit completion signal
        if any(keyword in message.lower() for keyword in ['complete', 'finished', 'done', 'no more']):
            if self.progress_callback:
                self.progress_callback({
                    'type': 'completion',
                    'message': message,
                    'total_deleted': self.deleted_count,
                    'status': 'complete'
                })

    def get_browser_screenshot(self):
        """Get a high-quality screenshot with page context"""
        if not self.driver:
            return None

        try:
            # Set browser window size for better quality
            self.driver.set_window_size(1200, 800)

            # Get screenshot AND page context
            screenshot_base64 = self.driver.get_screenshot_as_base64()
            current_url = self.driver.current_url
            page_title = self.driver.title

            # Store original dimensions for coordinate scaling
            screenshot_data = base64.b64decode(screenshot_base64)
            image = Image.open(io.BytesIO(screenshot_data))
            self.original_width = image.width
            self.original_height = image.height

            # High-quality resize for web display
            max_width = 1000
            max_height = 700

            # Calculate aspect ratio preserving dimensions
            aspect_ratio = image.width / image.height
            if aspect_ratio > max_width / max_height:
                new_width = max_width
                new_height = int(max_width / aspect_ratio)
            else:
                new_height = max_height
                new_width = int(max_height * aspect_ratio)

            # Use high-quality resampling
            resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Store display dimensions for coordinate scaling
            self.display_width = new_width
            self.display_height = new_height

            # Convert back to base64 with high quality
            buffer = io.BytesIO()
            resized_image.save(buffer, format='PNG', optimize=False, quality=95)
            resized_base64 = base64.b64encode(buffer.getvalue()).decode()

            # Create comprehensive screenshot data with context
            screenshot_data = {
                'screenshot': resized_base64,
                'url': current_url,
                'title': page_title,
                'timestamp': time.time(),
                'dimensions': {
                    'original': {'width': self.original_width, 'height': self.original_height},
                    'display': {'width': new_width, 'height': new_height}
                }
            }

            self.last_screenshot = screenshot_data

            # Log with URL context for debugging
            url_short = current_url[:60] + "..." if len(current_url) > 60 else current_url
            self._update_progress(f"📸 Screenshot: {self.original_width}x{self.original_height} → {new_width}x{new_height}")
            self._update_progress(f"🌐 URL: {url_short}")
            self._update_progress(f"📄 Title: {page_title[:50]}...")

            return screenshot_data

        except Exception as e:
            # Return error context instead of None
            error_data = {
                'error': str(e),
                'url': 'Connection Lost',
                'title': 'Browser Disconnected',
                'timestamp': time.time(),
                'screenshot': None
            }

            self._update_progress(f"❌ Screenshot error: {str(e)}")
            return error_data

    def enable_interactive_mode(self):
        """Enable interactive browser mode with screenshots"""
        self.screenshot_enabled = True
        self._update_progress("🖥️ Interactive browser mode enabled")

        # Try to automatically find and highlight the sign-in button
        self.find_and_highlight_signin()

        # Also check if we're on the right page
        current_url = self.driver.current_url
        if 'myactivity.google.com' in current_url and 'youtube_comments' not in current_url:
            self._update_progress("🔄 Navigating to YouTube comments page...")
            self.driver.get("https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en")

    def find_and_highlight_signin(self):
        """Find and highlight the sign-in button on the page"""
        try:
            # Look for the sign-in button with multiple strategies
            signin_selectors = [
                # Top right sign in button (blue button)
                'a[href*="accounts.google.com"][role="button"]',
                'a[data-ved*="Sign"]',
                'a.gb_A',  # Google bar sign in

                # Center sign in button (outlined button)
                'a[href*="ServiceLogin"]',
                'button:contains("Sign In")',
                'a:contains("Sign In")',

                # Generic selectors
                'a[aria-label="Sign In"]',
                'a[aria-label="Sign in"]',
                'button[aria-label="Sign in"]',
                'a.WpHeLc.VfPpkd-mRLv6',
                'a[jsname="hSRGPd"]',

                # Text-based selectors
                'a[href*="accounts.google.com"]',
                'button[type="submit"]'
            ]

            # First try CSS selectors
            for selector in signin_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.is_displayed():
                        return self._highlight_and_return_element(element, selector)
                except Exception as e:
                    continue

            # Try finding by text content using XPath
            text_selectors = [
                "//a[contains(text(), 'Sign In') or contains(text(), 'Sign in')]",
                "//button[contains(text(), 'Sign In') or contains(text(), 'Sign in')]",
                "//a[@href[contains(., 'accounts.google.com')]]",
                "//a[@href[contains(., 'ServiceLogin')]]"
            ]

            for xpath in text_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, xpath)
                    if element and element.is_displayed():
                        return self._highlight_and_return_element(element, f"XPath: {xpath}")
                except Exception as e:
                    continue

            # Try JavaScript-based search as last resort
            try:
                element = self.driver.execute_script("""
                    // Find all links and buttons
                    var elements = document.querySelectorAll('a, button');
                    for (var i = 0; i < elements.length; i++) {
                        var el = elements[i];
                        var text = el.textContent.toLowerCase();
                        var href = el.href || '';

                        // Check for sign in text or Google accounts URL
                        if (text.includes('sign in') ||
                            href.includes('accounts.google.com') ||
                            href.includes('ServiceLogin')) {
                            return el;
                        }
                    }
                    return null;
                """)

                if element:
                    return self._highlight_and_return_element(element, "JavaScript search")

            except Exception as e:
                self._update_progress(f"❌ JavaScript search failed: {str(e)}")

            self._update_progress("⚠️ Sign-In button not found - you may need to navigate to the sign-in page first")
            return None

        except Exception as e:
            self._update_progress(f"❌ Error finding sign-in button: {str(e)}")
            return None

    def _highlight_and_return_element(self, element, selector):
        """Helper method to highlight element and return info"""
        try:
            # Highlight the element
            self.driver.execute_script("""
                arguments[0].style.border = '3px solid red';
                arguments[0].style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
                arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});
            """, element)

            # Get element info
            element_info = {
                'tag': element.tag_name,
                'text': element.text[:50] if element.text else 'No text',
                'href': element.get_attribute('href') if element.get_attribute('href') else 'No href',
                'aria_label': element.get_attribute('aria-label') if element.get_attribute('aria-label') else 'No aria-label',
                'class': element.get_attribute('class') if element.get_attribute('class') else 'No class',
                'selector': selector
            }

            self._update_progress(f"🎯 Found Sign-In button: {element_info}")
            self._update_progress("🔴 Sign-In button highlighted in red - you can click it!")
            return element

        except Exception as e:
            self._update_progress(f"❌ Error highlighting element: {str(e)}")
            return element

    def auto_click_signin(self):
        """Automatically click the sign-in button"""
        try:
            signin_element = self.find_and_highlight_signin()
            if signin_element:
                # Try multiple click methods to bypass automation detection
                try:
                    # Method 1: Regular click
                    signin_element.click()
                    self._update_progress("✅ Clicked Sign-In button (method 1)")
                except Exception as e1:
                    try:
                        # Method 2: JavaScript click
                        self.driver.execute_script("arguments[0].click();", signin_element)
                        self._update_progress("✅ Clicked Sign-In button (method 2 - JavaScript)")
                    except Exception as e2:
                        try:
                            # Method 3: ActionChains click
                            from selenium.webdriver.common.action_chains import ActionChains
                            ActionChains(self.driver).move_to_element(signin_element).click().perform()
                            self._update_progress("✅ Clicked Sign-In button (method 3 - ActionChains)")
                        except Exception as e3:
                            # Method 4: Navigate directly to href
                            href = signin_element.get_attribute('href')
                            if href:
                                self.driver.get(href)
                                self._update_progress("✅ Navigated to Sign-In URL directly")
                            else:
                                self._update_progress(f"❌ All click methods failed: {e1}, {e2}, {e3}")
                                return False

                # Wait a moment for page to load
                time.sleep(2)
                self._update_progress("🌐 Sign-In page should be loading...")

                # Force screenshot update to show new page
                if self.screenshot_enabled:
                    time.sleep(1)  # Extra wait for page to fully load
                    screenshot = self.get_browser_screenshot()
                    if screenshot:
                        self._update_progress("📸 Updated screenshot with new page")
                        # Send screenshot update via socketio if available
                        try:
                            import ultra_fast_app
                            if hasattr(ultra_fast_app, 'socketio'):
                                ultra_fast_app.socketio.emit('browser_screenshot', {'screenshot': screenshot})
                        except:
                            pass

                return True
            else:
                self._update_progress("❌ Could not find Sign-In button to click")
                return False
        except Exception as e:
            self._update_progress(f"❌ Error clicking sign-in button: {str(e)}")
            return False

    def click_at_coordinates(self, x, y):
        """Click at specific coordinates on the page with proper scaling"""
        if not self.driver:
            return False

        try:
            # Scale coordinates from display size to original browser size
            if hasattr(self, 'display_width') and hasattr(self, 'original_width'):
                scale_x = self.original_width / self.display_width
                scale_y = self.original_height / self.display_height

                actual_x = int(x * scale_x)
                actual_y = int(y * scale_y)

                # Ensure coordinates are within browser bounds
                actual_x = max(0, min(actual_x, self.original_width - 1))
                actual_y = max(0, min(actual_y, self.original_height - 1))
            else:
                actual_x, actual_y = x, y

            self._update_progress(f"🖱️ Clicking at display ({x}, {y}) → browser ({actual_x}, {actual_y}) [Browser: {self.original_width}x{self.original_height}]")

            # First, let's see what we're actually clicking on
            element_info = self.driver.execute_script(f"""
                var element = document.elementFromPoint({actual_x}, {actual_y});
                if (element) {{
                    return {{
                        tag: element.tagName,
                        id: element.id || 'no-id',
                        className: element.className || 'no-class',
                        text: element.textContent ? element.textContent.substring(0, 30) : 'no-text',
                        type: element.type || 'no-type',
                        clickable: element.onclick !== null || element.addEventListener !== undefined,
                        visible: element.offsetParent !== null,
                        rect: element.getBoundingClientRect()
                    }};
                }} else {{
                    return {{ error: 'No element found at coordinates' }};
                }}
            """)

            self._update_progress(f"🎯 Target element: {element_info}")

            # Multiple click strategies for maximum compatibility
            success = False

            # Strategy 1: Simple element click (most reliable)
            try:
                element = self.driver.execute_script(f"return document.elementFromPoint({actual_x}, {actual_y});")
                if element:
                    # Scroll element into view first
                    self.driver.execute_script("arguments[0].scrollIntoView({{behavior: 'instant', block: 'center'}});", element)
                    # Wait a moment for scroll
                    import time
                    time.sleep(0.2)
                    # Try direct element click
                    element.click()
                    self._update_progress(f"✅ Direct element click successful!")
                    success = True
                else:
                    self._update_progress(f"⚠️ No element found at coordinates ({actual_x}, {actual_y})")
            except Exception as e:
                self._update_progress(f"⚠️ Element click failed: {str(e)}")

            # Strategy 2: ActionChains click if element click failed
            if not success:
                try:
                    from selenium.webdriver.common.action_chains import ActionChains
                    # Move to element and click
                    element = self.driver.execute_script(f"return document.elementFromPoint({actual_x}, {actual_y});")
                    if element:
                        ActionChains(self.driver).move_to_element(element).click().perform()
                        self._update_progress(f"✅ ActionChains element click successful!")
                        success = True
                    else:
                        self._update_progress(f"⚠️ No element for ActionChains")
                except Exception as e:
                    self._update_progress(f"⚠️ ActionChains failed: {str(e)}")

            # Strategy 3: Simple JavaScript click as last resort
            if not success:
                try:
                    self.driver.execute_script(f"""
                        var element = document.elementFromPoint({actual_x}, {actual_y});
                        if (element) {{
                            element.click();
                        }}
                    """)
                    self._update_progress(f"✅ JavaScript click executed")
                    success = True
                except Exception as e:
                    self._update_progress(f"❌ JavaScript click failed: {str(e)}")

            if success:
                # Wait a moment for any page changes
                import time
                time.sleep(0.5)
                return True
            else:
                self._update_progress(f"❌ All click strategies failed for coordinates ({actual_x}, {actual_y})")
                return False

        except Exception as e:
            self._update_progress(f"❌ Click error: {str(e)}")
            return False

    def type_text(self, text):
        """Type text in the currently focused element"""
        if not self.driver:
            return False

        try:
            # Handle special keys
            if text == '\b':  # Backspace
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\b')
                self._update_progress("⌫ Backspace")
                return True
            elif text == '\n':  # Enter
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\n')
                self._update_progress("↵ Enter")
                return True
            elif text == '\t':  # Tab
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\t')
                self._update_progress("⇥ Tab")
                return True
            else:
                # Regular text
                active_element = self.driver.switch_to.active_element
                active_element.send_keys(text)
                self._update_progress(f"⌨️ Typed: '{text}'")
                return True

        except Exception as e:
            self._update_progress(f"❌ Type error: {str(e)}")
            return False

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.screenshot_enabled = False
            self.last_screenshot = None
