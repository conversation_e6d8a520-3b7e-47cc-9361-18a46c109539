import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import threading
from concurrent.futures import ThreadPoolExecutor
import base64
import io
from PIL import Image

class UltraFastCommentDeleter:
    def __init__(self, progress_callback=None, refresh_interval=10, max_stuck_time=8):
        self.driver = None
        self.progress_callback = progress_callback
        self.deleted_count = 0
        self.target_speed = 4  # 4 deletes per second
        self.delete_interval = 1.0 / self.target_speed  # 0.25 seconds between deletes

        # Auto-refresh settings - configurable by user
        self.refresh_interval = refresh_interval  # User configurable refresh interval
        self.max_stuck_time = max_stuck_time     # User configurable stuck detection
        self.last_progress_time = time.time()
        self.last_deleted_count = 0

        # Performance tracking for smart refresh
        self.deletion_times = []
        self.last_deletion_time = time.time()
        self.performance_refresh_threshold = 25  # Refresh every 25 deletions
        self.speed_degradation_threshold = 1.5   # Refresh if speed drops 50%

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Browser interaction settings
        self.screenshot_enabled = False
        self.last_screenshot = None
        self.original_width = 1200
        self.original_height = 800
        self.display_width = 1000
        self.display_height = 700

    def update_refresh_settings(self, refresh_interval=None, max_stuck_time=None):
        """Update refresh settings during runtime"""
        if refresh_interval is not None:
            self.refresh_interval = refresh_interval
            self._update_progress(f"🔄 Refresh interval updated to {refresh_interval}s")

        if max_stuck_time is not None:
            self.max_stuck_time = max_stuck_time
            self._update_progress(f"⏱️ Stuck detection updated to {max_stuck_time}s")

    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver optimized for interaction"""
        try:
            chrome_options = ChromeOptions()

            # Basic optimizations (keep JavaScript enabled for interaction)
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            # Removed --disable-javascript to allow interactions
            # Removed --disable-images to see what we're clicking

            # Interaction-friendly settings
            chrome_options.add_argument("--enable-automation")  # Allow automation
            chrome_options.add_argument("--disable-web-security")  # Allow cross-origin
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-iframes-sandbox")

            # Anti-detection (minimal)
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Add user agent to appear more normal
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            if headless:
                chrome_options.add_argument("--headless")

            # Get ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set reasonable timeouts for interaction
            self.driver.implicitly_wait(2)  # Longer wait for elements
            self.driver.set_page_load_timeout(30)  # Longer page load timeout

            # Execute script to remove webdriver detection
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self._update_progress("Interactive browser started!")
            return True

        except Exception as e:
            self._update_progress(f"Failed to start browser: {str(e)}")
            return False

    def go_to_activity_page(self):
        """Navigate to YouTube comments page"""
        try:
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self._update_progress("Navigating to YouTube Comments page...")

            self.driver.get(url)

            # Quick wait for page load
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            self._update_progress("Comments page loaded. Please sign in if needed.")
            return True

        except Exception as e:
            self._update_progress(f"Failed to navigate: {str(e)}")
            return False

    def wait_for_login(self):
        """Quick login check"""
        self._update_progress("⏳ Checking login status...")

        try:
            # Quick check for logged-in state
            WebDriverWait(self.driver, 30).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("✅ Login confirmed! Ready for ultra-fast deletion.")
            return True
        except TimeoutException:
            self._update_progress("❌ Login timeout. Please ensure you're signed in.")
            return False

    def _is_logged_in(self):
        """Enhanced login check with specific YouTube Comments detection"""
        try:
            # Primary check: Look for the specific "Your YouTube Comments" element
            youtube_comments_element = self.driver.find_elements(By.XPATH,
                "//div[@class='jPCT6' and text()='Your YouTube Comments']")

            if youtube_comments_element:
                self._update_progress("🎯 Found 'Your YouTube Comments' section - login confirmed!")
                return True

            # Secondary check: Look for other comment page indicators
            comment_elements = self.driver.find_elements(By.CSS_SELECTOR,
                'div[data-item-id], .QTGV3c, [aria-label*="Delete"], .VfPpkd-Bz112c')

            if comment_elements:
                self._update_progress("✅ Found comment elements - login detected!")
                return True

            # Tertiary check: Look for general activity content
            indicators = self.driver.find_elements(By.CSS_SELECTOR, "div[data-ved], c-wiz")

            if len(indicators) > 0:
                # Check current URL for additional context
                current_url = self.driver.current_url
                if "myactivity.google.com" in current_url and "youtube_comments" in current_url:
                    self._update_progress("🔍 On YouTube comments page, waiting for content to load...")
                    return False  # Content not fully loaded yet
                else:
                    self._update_progress("🔍 Found activity content, checking for YouTube comments...")
                    return False  # Not on the right page yet

            # Check for sign-in elements (indicates not logged in)
            signin_elements = self.driver.find_elements(By.CSS_SELECTOR,
                'a[href*="ServiceLogin"], button[aria-label*="Sign"], .gb_Ua')

            if signin_elements:
                self._update_progress("❌ Sign-in button detected - please complete login")
                return False

            return False
        except Exception as e:
            self._update_progress(f"⚠️ Error checking login status: {str(e)}")
            return False

    def ultra_fast_delete_all(self):
        """Ultra-fast deletion using JavaScript and optimized strategies with auto-refresh"""
        try:
            self._update_progress("🚀 Starting ULTRA-FAST deletion (4 deletes/second)...")
            self._update_progress(f"🔄 Auto-refresh enabled: every {self.refresh_interval}s or when stuck for {self.max_stuck_time}s")

            start_time = time.time()
            self.last_progress_time = start_time

            while True:
                cycle_start = time.time()
                initial_count = self.deleted_count

                # Method 1: Pure JavaScript batch deletion (fastest)
                deleted_js = self._javascript_batch_delete()

                if deleted_js > 0:
                    self._update_progress(f"JavaScript batch deleted {deleted_js} comments!")
                    self.deleted_count += deleted_js
                    self._update_progress_tracking()

                # Method 2: Optimized Selenium for remaining comments
                selenium_deleted = self._optimized_selenium_delete()

                # Check if we made progress this cycle
                cycle_deleted = self.deleted_count - initial_count

                if cycle_deleted == 0:
                    # No progress made, check if we need to refresh
                    if self._should_refresh():
                        self._refresh_page()
                        continue
                    else:
                        # No comments found and no need to refresh - we're done
                        self._update_progress("🔍 No more comments found - deletion process complete!")
                        break
                else:
                    self._update_progress_tracking()

                # Check for time-based refresh
                if time.time() - cycle_start > self.refresh_interval:
                    self._update_progress(f"⏰ {self.refresh_interval}s interval reached - refreshing page")
                    self._refresh_page()

            # Send completion status
            completion_message = f"🎉 Ultra-fast deletion complete! Total: {self.deleted_count}"
            self._update_progress(completion_message)

            # Send specific completion signal
            if self.progress_callback:
                self.progress_callback({
                    'type': 'completion',
                    'message': completion_message,
                    'total_deleted': self.deleted_count,
                    'status': 'complete'
                })

        except Exception as e:
            error_message = f"Error during ultra-fast deletion: {str(e)}"
            self._update_progress(error_message)

            # Send specific error signal
            if self.progress_callback:
                self.progress_callback({
                    'type': 'error',
                    'message': error_message,
                    'status': 'error'
                })

    def _should_refresh(self):
        """Check if page should be refreshed due to being stuck OR performance degradation"""
        current_time = time.time()
        time_since_progress = current_time - self.last_progress_time

        # Check for stuck state (no progress)
        if time_since_progress > self.max_stuck_time:
            self._update_progress(f"⚠️ No progress for {time_since_progress:.1f}s - page may be stuck")
            return True

        # Check for performance degradation
        if self._should_refresh_for_performance():
            return True

        return False

    def _should_refresh_for_performance(self):
        """Check if we need to refresh due to performance degradation"""

        # Refresh after every N deletions to prevent performance degradation
        if self.deleted_count > 0 and self.deleted_count % self.performance_refresh_threshold == 0:
            self._update_progress(f"🔄 Performance refresh after {self.deleted_count} deletions")
            return True

        # Check if deletion speed has degraded significantly
        if len(self.deletion_times) >= 10:
            recent_avg = sum(self.deletion_times[-5:]) / 5  # Last 5 deletions
            initial_avg = sum(self.deletion_times[:5]) / 5   # First 5 deletions

            if recent_avg > initial_avg * self.speed_degradation_threshold:
                slowdown_percent = ((recent_avg - initial_avg) / initial_avg) * 100
                self._update_progress(f"📉 Speed degraded by {slowdown_percent:.1f}% - refreshing for performance")
                return True

        return False

    def _track_deletion_performance(self):
        """Track deletion performance for smart refresh decisions"""
        current_time = time.time()
        deletion_time = current_time - self.last_deletion_time

        self.deletion_times.append(deletion_time)

        # Keep only last 50 deletion times to prevent memory bloat
        if len(self.deletion_times) > 50:
            self.deletion_times = self.deletion_times[-50:]

        self.last_deletion_time = current_time

        # Log performance stats occasionally
        if self.deleted_count % 10 == 0 and len(self.deletion_times) >= 5:
            recent_avg = sum(self.deletion_times[-5:]) / 5
            self._update_progress(f"📊 Recent deletion speed: {1/recent_avg:.1f} deletes/second")

    def _update_progress_tracking(self):
        """Update progress tracking variables"""
        self.last_progress_time = time.time()
        self.last_deleted_count = self.deleted_count

    def _refresh_page(self):
        """Performance-focused refresh that mimics manual refresh behavior"""
        try:
            self._update_progress("🔄 Performance refresh (manual-like)...")

            # Store current URL
            current_url = self.driver.current_url

            # Clear browser state like manual refresh does
            try:
                self.driver.execute_script("window.localStorage.clear();")
                self.driver.execute_script("window.sessionStorage.clear();")
            except:
                pass

            # Hard refresh with cache clear (like Ctrl+F5)
            self.driver.execute_script("location.reload(true);")

            # Wait for complete page load (like manual observation)
            WebDriverWait(self.driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Wait for comment elements to be fully loaded and interactive
            try:
                WebDriverWait(self.driver, 8).until(
                    lambda driver: len(driver.find_elements(By.CSS_SELECTOR, "button")) > 5
                )
            except:
                # Fallback wait
                time.sleep(3)

            # Additional wait for JavaScript to settle (like manual observation)
            time.sleep(2)

            # Verify we're still on the right page
            if "myactivity.google.com" not in self.driver.current_url:
                self._update_progress("⚠️ Page changed, navigating back...")
                self.driver.get(current_url)
                time.sleep(2)

            # Reset performance tracking after refresh
            self.deletion_times = []  # Clear performance history
            self.last_deletion_time = time.time()

            self._update_progress("✅ Performance refresh complete - speed should be restored")
            self._update_progress_tracking()  # Reset stuck timer

        except Exception as e:
            self._update_progress(f"❌ Refresh error: {str(e)}")
            time.sleep(2)

    def _javascript_batch_delete(self):
        """Use JavaScript for maximum speed deletion"""
        self._update_progress("🔥 Attempting JavaScript batch deletion...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        js_script = f"""
        // Ultra-fast JavaScript deletion
        console.log('Starting ultra-fast deletion...');

        // Hamburger menu path to avoid
        const hamburgerMenuPath = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z";

        // Find all X buttons with the specific path, avoiding hamburger menu
        const selectors = [
            'svg.TjcpUd path[d="{x_button_path}"]',
            'svg.NMm5M path[d="{x_button_path}"]',
            'path[d="{x_button_path}"]',
            'svg[width="24"][height="24"] path[d*="19 6.41"]'
        ];

        let allXButtons = [];
        selectors.forEach(selector => {{
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(path => {{
                // Skip if this is the hamburger menu
                if (path.getAttribute('d') === hamburgerMenuPath) {{
                    return;
                }}

                const button = path.closest('button');
                if (button && !allXButtons.includes(button)) {{
                    // Double-check the button doesn't contain hamburger menu
                    const hamburgerPaths = button.querySelectorAll(`path[d="${{hamburgerMenuPath}}"]`);
                    if (hamburgerPaths.length === 0) {{
                        allXButtons.push(button);
                    }}
                }}
            }});
        }});

        console.log('Found', allXButtons.length, 'delete buttons (excluding hamburger menus)');

        // Delete at 4 per second (250ms intervals)
        let deleted = 0;
        const deleteInterval = 250; // 250ms = 4 per second

        allXButtons.forEach((button, index) => {{
            setTimeout(() => {{
                try {{
                    // Click the delete button
                    button.click();
                    deleted++;
                    console.log('Deleted comment', deleted);

                    // Try to auto-confirm if confirmation dialog appears
                    setTimeout(() => {{
                        const confirmButtons = document.querySelectorAll('button');
                        confirmButtons.forEach(btn => {{
                            const text = btn.textContent.toLowerCase();
                            if (text.includes('delete') || text.includes('remove') || text.includes('confirm')) {{
                                btn.click();
                            }}
                        }});
                    }}, 50);

                }} catch (e) {{
                    console.error('Error deleting comment', index, e);
                }}
            }}, index * deleteInterval);
        }});

        // Return the number of buttons found
        return allXButtons.length;
        """

        try:
            result = self.driver.execute_script(js_script)
            return result or 0
        except Exception as e:
            self._update_progress(f"JavaScript deletion failed: {str(e)}")
            return 0

    def _optimized_selenium_delete(self):
        """Optimized Selenium deletion for remaining comments"""
        self._update_progress("🔧 Running optimized Selenium cleanup...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        # Hamburger menu path to avoid
        hamburger_menu_path = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"

        # Ultra-fast selectors (most specific first), avoiding hamburger menu
        fast_selectors = [
            (By.CSS_SELECTOR, f'svg.TjcpUd path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, f'svg.NMm5M path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, 'svg[width="24"] path[d*="19 6.41"]'),
        ]

        start_time = time.time()
        deleted_this_round = 0

        while True:
            found_any = False
            round_start_count = self.deleted_count

            for by_method, selector in fast_selectors:
                try:
                    # Find elements quickly
                    elements = self.driver.find_elements(by_method, selector)

                    if elements:
                        found_any = True

                        # Process in batches for speed
                        batch_size = min(4, len(elements))  # 4 at a time for 4/second
                        batch = elements[:batch_size]

                        for path_element in batch:
                            try:
                                # Check if this path is the hamburger menu
                                path_d = path_element.get_attribute('d')
                                if path_d == hamburger_menu_path:
                                    continue  # Skip hamburger menu

                                # Find parent button quickly
                                button = path_element.find_element(By.XPATH, "./ancestor::button[1]")

                                # Double-check button doesn't contain hamburger menu
                                hamburger_paths = button.find_elements(By.CSS_SELECTOR, f'path[d="{hamburger_menu_path}"]')
                                if hamburger_paths:
                                    continue  # Skip if button contains hamburger menu

                                # Ultra-fast click
                                self.driver.execute_script("arguments[0].click();", button)

                                # Quick confirmation attempt
                                try:
                                    confirm_btn = WebDriverWait(self.driver, 0.1).until(
                                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Delete')]"))
                                    )
                                    self.driver.execute_script("arguments[0].click();", confirm_btn)
                                except TimeoutException:
                                    pass  # No confirmation needed

                                self.deleted_count += 1
                                deleted_this_round += 1
                                self._update_progress(f"⚡ Fast deleted #{self.deleted_count}")
                                self._update_progress_tracking()  # Update progress tracking
                                self._track_deletion_performance()  # Track performance for smart refresh

                                # Maintain 4/second speed
                                time.sleep(self.delete_interval)

                            except Exception as e:
                                continue

                        break  # Found elements with this selector, don't try others

                except Exception as e:
                    continue

            if not found_any:
                break

            # Check if we made progress this round
            round_deleted = self.deleted_count - round_start_count
            if round_deleted == 0:
                # No progress in this round, might be stuck
                break

            # Quick scroll to load more
            self.driver.execute_script("window.scrollBy(0, 1000);")
            time.sleep(0.5)  # Brief pause for loading

            # Safety timeout (don't run forever)
            if time.time() - start_time > 300:  # 5 minutes max
                self._update_progress("⏰ Safety timeout reached")
                break

        return deleted_this_round

    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)

        # Check for completion keywords and send explicit completion signal
        if any(keyword in message.lower() for keyword in ['complete', 'finished', 'done', 'no more']):
            if self.progress_callback:
                self.progress_callback({
                    'type': 'completion',
                    'message': message,
                    'total_deleted': self.deleted_count,
                    'status': 'complete'
                })

    def get_browser_screenshot(self, priority_reason=None):
        """Get an optimized screenshot with URL change detection and priority handling"""
        if not self.driver:
            return None

        try:
            # Get screenshot directly without resizing browser window (faster)
            screenshot_base64 = self.driver.get_screenshot_as_base64()
            current_url = self.driver.current_url
            page_title = self.driver.title
            timestamp = time.time()

            # Detect URL changes for priority handling
            url_changed = not hasattr(self, '_last_url') or self._last_url != current_url

            # Fast resize using browser-native scaling instead of PIL
            screenshot_data = {
                'screenshot': screenshot_base64,
                'url': current_url,
                'title': page_title,
                'timestamp': timestamp,
                'fast_mode': True,
                'url_changed': url_changed,
                'priority_reason': priority_reason,  # 'click', 'type', 'url_change', etc.
                'action_timestamp': timestamp if priority_reason else None
            }

            # Store dimensions for coordinate scaling (only when needed)
            if not hasattr(self, 'original_width'):
                # Only decode and get dimensions once
                img_data = base64.b64decode(screenshot_base64)
                image = Image.open(io.BytesIO(img_data))
                self.original_width = image.width
                self.original_height = image.height
                self.display_width = 1000  # Fixed display width
                self.display_height = int(1000 * image.height / image.width)

            self.last_screenshot = screenshot_data

            # Enhanced logging for URL changes and actions
            if url_changed:
                self._last_url = current_url
                url_short = current_url[:80] + "..." if len(current_url) > 80 else current_url
                self._update_progress(f"🌐 URL CHANGED: {url_short}")
                self._update_progress(f"📄 Page Title: {page_title[:60]}...")

                # Mark this as a priority screenshot
                screenshot_data['priority_reason'] = 'url_change'
                screenshot_data['url_changed'] = True
            elif priority_reason:
                # Log priority actions
                action_icons = {
                    'click': '🖱️',
                    'type': '⌨️',
                    'auto_action': '🤖',
                    'user_request': '👤'
                }
                icon = action_icons.get(priority_reason, '⚡')
                self._update_progress(f"{icon} ACTION: {priority_reason} - Screenshot updated")

            return screenshot_data

        except Exception as e:
            # Return error context instead of None
            error_data = {
                'error': str(e),
                'url': 'Connection Lost',
                'title': 'Browser Disconnected',
                'timestamp': time.time(),
                'screenshot': None,
                'priority_reason': 'error'
            }

            self._update_progress(f"❌ Screenshot error: {str(e)}")
            return error_data

    def enable_interactive_mode(self):
        """Enable interactive browser mode with screenshots"""
        self.screenshot_enabled = True
        self._update_progress("🖥️ Interactive browser mode enabled")

        # Try to automatically find and highlight the sign-in button
        self.find_and_highlight_signin()

        # Also check if we're on the right page
        current_url = self.driver.current_url
        if 'myactivity.google.com' in current_url and 'youtube_comments' not in current_url:
            self._update_progress("🔄 Navigating to YouTube comments page...")
            self.driver.get("https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en")

    def find_and_highlight_signin(self):
        """Find and highlight the sign-in button on the page"""
        try:
            # Look for the sign-in button with multiple strategies
            signin_selectors = [
                # Top right sign in button (blue button)
                'a[href*="accounts.google.com"][role="button"]',
                'a[data-ved*="Sign"]',
                'a.gb_A',  # Google bar sign in

                # Center sign in button (outlined button)
                'a[href*="ServiceLogin"]',
                'button:contains("Sign In")',
                'a:contains("Sign In")',

                # Generic selectors
                'a[aria-label="Sign In"]',
                'a[aria-label="Sign in"]',
                'button[aria-label="Sign in"]',
                'a.WpHeLc.VfPpkd-mRLv6',
                'a[jsname="hSRGPd"]',

                # Text-based selectors
                'a[href*="accounts.google.com"]',
                'button[type="submit"]'
            ]

            # First try CSS selectors
            for selector in signin_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element and element.is_displayed():
                        return self._highlight_and_return_element(element, selector)
                except Exception as e:
                    continue

            # Try finding by text content using XPath
            text_selectors = [
                "//a[contains(text(), 'Sign In') or contains(text(), 'Sign in')]",
                "//button[contains(text(), 'Sign In') or contains(text(), 'Sign in')]",
                "//a[@href[contains(., 'accounts.google.com')]]",
                "//a[@href[contains(., 'ServiceLogin')]]"
            ]

            for xpath in text_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, xpath)
                    if element and element.is_displayed():
                        return self._highlight_and_return_element(element, f"XPath: {xpath}")
                except Exception as e:
                    continue

            # Try JavaScript-based search as last resort
            try:
                element = self.driver.execute_script("""
                    // Find all links and buttons
                    var elements = document.querySelectorAll('a, button');
                    for (var i = 0; i < elements.length; i++) {
                        var el = elements[i];
                        var text = el.textContent.toLowerCase();
                        var href = el.href || '';

                        // Check for sign in text or Google accounts URL
                        if (text.includes('sign in') ||
                            href.includes('accounts.google.com') ||
                            href.includes('ServiceLogin')) {
                            return el;
                        }
                    }
                    return null;
                """)

                if element:
                    return self._highlight_and_return_element(element, "JavaScript search")

            except Exception as e:
                self._update_progress(f"❌ JavaScript search failed: {str(e)}")

            self._update_progress("⚠️ Sign-In button not found - you may need to navigate to the sign-in page first")
            return None

        except Exception as e:
            self._update_progress(f"❌ Error finding sign-in button: {str(e)}")
            return None

    def _highlight_and_return_element(self, element, selector):
        """Helper method to highlight element and return info"""
        try:
            # Highlight the element
            self.driver.execute_script("""
                arguments[0].style.border = '3px solid red';
                arguments[0].style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
                arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});
            """, element)

            # Get element info
            element_info = {
                'tag': element.tag_name,
                'text': element.text[:50] if element.text else 'No text',
                'href': element.get_attribute('href') if element.get_attribute('href') else 'No href',
                'aria_label': element.get_attribute('aria-label') if element.get_attribute('aria-label') else 'No aria-label',
                'class': element.get_attribute('class') if element.get_attribute('class') else 'No class',
                'selector': selector
            }

            self._update_progress(f"🎯 Found Sign-In button: {element_info}")
            self._update_progress("🔴 Sign-In button highlighted in red - you can click it!")
            return element

        except Exception as e:
            self._update_progress(f"❌ Error highlighting element: {str(e)}")
            return element

    def auto_click_signin(self):
        """Automatically click the sign-in button"""
        try:
            signin_element = self.find_and_highlight_signin()
            if signin_element:
                # Try multiple click methods to bypass automation detection
                try:
                    # Method 1: Regular click
                    signin_element.click()
                    self._update_progress("✅ Clicked Sign-In button (method 1)")
                except Exception as e1:
                    try:
                        # Method 2: JavaScript click
                        self.driver.execute_script("arguments[0].click();", signin_element)
                        self._update_progress("✅ Clicked Sign-In button (method 2 - JavaScript)")
                    except Exception as e2:
                        try:
                            # Method 3: ActionChains click
                            from selenium.webdriver.common.action_chains import ActionChains
                            ActionChains(self.driver).move_to_element(signin_element).click().perform()
                            self._update_progress("✅ Clicked Sign-In button (method 3 - ActionChains)")
                        except Exception as e3:
                            # Method 4: Navigate directly to href
                            href = signin_element.get_attribute('href')
                            if href:
                                self.driver.get(href)
                                self._update_progress("✅ Navigated to Sign-In URL directly")
                            else:
                                self._update_progress(f"❌ All click methods failed: {e1}, {e2}, {e3}")
                                return False

                # Wait a moment for page to load
                time.sleep(3)
                self._update_progress("🌐 Sign-In page should be loading...")

                # Check if we're now on Google sign-in page
                current_url = self.driver.current_url
                if "accounts.google.com" in current_url:
                    self._update_progress("✅ Successfully navigated to Google sign-in page!")
                    self.handle_google_signin_page()

                # Force screenshot update to show new page
                if self.screenshot_enabled:
                    time.sleep(1)  # Extra wait for page to fully load
                    screenshot = self.get_browser_screenshot()
                    if screenshot:
                        self._update_progress("📸 Updated screenshot with new page")
                        # Send screenshot update via socketio if available
                        try:
                            import ultra_fast_app
                            if hasattr(ultra_fast_app, 'socketio'):
                                ultra_fast_app.socketio.emit('browser_screenshot', {'screenshot': screenshot})
                        except:
                            pass

                return True
            else:
                self._update_progress("❌ Could not find Sign-In button to click")
                return False
        except Exception as e:
            self._update_progress(f"❌ Error clicking sign-in button: {str(e)}")
            return False

    def handle_google_signin_page(self):
        """Handle Google sign-in page interactions"""
        try:
            self._update_progress("🔍 Analyzing Google sign-in page...")

            # Look for email input field
            email_selectors = [
                "#identifierId",  # Most common ID
                "input[type='email'][name='identifier']",
                "input[aria-label*='Email']",
                "input[autocomplete='username']",
                "input[name='identifier']"
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_input and email_input.is_displayed():
                        self._update_progress(f"✅ Found email input field: {selector}")
                        break
                except Exception:
                    continue

            if email_input:
                self._update_progress("📧 Email input field detected!")
                self._update_progress("💡 You can now:")
                self._update_progress("   • Click on the email field in the browser")
                self._update_progress("   • Type your email address")
                self._update_progress("   • Press Enter or click Next")

                # Highlight the email field
                try:
                    self.driver.execute_script("""
                        arguments[0].style.border = '3px solid #ff0000';
                        arguments[0].style.backgroundColor = '#ffeeee';
                        arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});
                    """, email_input)
                    self._update_progress("🎯 Email field highlighted in red")
                except Exception:
                    pass

                # Look for Next button
                next_selectors = [
                    "button[type='submit']",
                    "//button[contains(text(), 'Next')]",
                    "//button[contains(text(), 'Continue')]",
                    ".VfPpkd-LgbsSe",
                    "button[jsname*='LgbsSe']"
                ]

                for selector in next_selectors:
                    try:
                        if selector.startswith("//"):
                            next_button = self.driver.find_element(By.XPATH, selector)
                        else:
                            next_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if next_button and next_button.is_displayed():
                            # Highlight the Next button too
                            try:
                                self.driver.execute_script("""
                                    arguments[0].style.border = '3px solid #0066cc';
                                    arguments[0].style.backgroundColor = '#e6f3ff';
                                """, next_button)
                                self._update_progress("🔵 Next button highlighted in blue")
                            except Exception:
                                pass
                            break
                    except Exception:
                        continue

                return True
            else:
                self._update_progress("❌ Email input field not found")

                # Look for other interactive elements
                interactive_selectors = [
                    "input[type='text']",
                    "input[type='email']",
                    "button[type='submit']",
                    "//button[contains(text(), 'Next')]",
                    "//button[contains(text(), 'Continue')]"
                ]

                found_elements = []
                for selector in interactive_selectors:
                    try:
                        if selector.startswith("//"):
                            elements = self.driver.find_elements(By.XPATH, selector)
                        else:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                        for element in elements:
                            if element.is_displayed():
                                found_elements.append({
                                    'tag': element.tag_name,
                                    'type': element.get_attribute('type'),
                                    'name': element.get_attribute('name'),
                                    'id': element.get_attribute('id'),
                                    'text': element.text[:30] if element.text else 'No text'
                                })
                    except Exception:
                        continue

                if found_elements:
                    self._update_progress(f"🔍 Found {len(found_elements)} interactive elements:")
                    for elem in found_elements[:5]:  # Show first 5
                        self._update_progress(f"   • {elem}")

                return True

        except Exception as e:
            self._update_progress(f"❌ Error handling Google sign-in page: {str(e)}")
            return False

    def test_google_credentials(self, email, password):
        """Test Google credentials by attempting to sign in"""
        try:
            self._update_progress(f"🧪 Testing credentials for {email}...")

            # Navigate to Google sign-in page
            self.driver.get("https://accounts.google.com/signin")
            time.sleep(3)

            # Fill in email
            email_input = self.driver.find_element(By.ID, "identifierId")
            email_input.clear()
            email_input.send_keys(email)

            # Click Next
            next_button = self.driver.find_element(By.ID, "identifierNext")
            next_button.click()
            time.sleep(3)

            # Check if we reached password page
            try:
                password_input = self.driver.find_element(By.NAME, "password")
                self._update_progress("✅ Email accepted, testing password...")

                # Fill in password
                password_input.clear()
                password_input.send_keys(password)

                # Click Next
                password_next = self.driver.find_element(By.ID, "passwordNext")
                password_next.click()
                time.sleep(5)

                # Check if login was successful (no error messages)
                current_url = self.driver.current_url
                if "accounts.google.com" not in current_url or "myaccount.google.com" in current_url:
                    self._update_progress("✅ Credentials test successful!")
                    return True
                else:
                    # Check for error messages
                    try:
                        error_elements = self.driver.find_elements(By.CSS_SELECTOR, "[role='alert'], .LXRPh, .dEOOab")
                        if error_elements:
                            error_text = error_elements[0].text
                            self._update_progress(f"❌ Login error: {error_text}")
                            return False
                    except:
                        pass

                    self._update_progress("❌ Login failed - still on sign-in page")
                    return False

            except Exception as e:
                self._update_progress(f"❌ Password page not reached: {str(e)}")
                return False

        except Exception as e:
            self._update_progress(f"❌ Error testing credentials: {str(e)}")
            return False

    def auto_signin_with_credentials(self, email, password):
        """Automatically sign in using provided credentials"""
        try:
            self._update_progress(f"🔑 Auto-signing in with {email}...")

            current_url = self.driver.current_url

            # If not on Google sign-in page, navigate there first
            if "accounts.google.com" not in current_url:
                # Try to click sign-in button first
                if not self.auto_click_signin():
                    # If that fails, navigate directly
                    self.driver.get("https://accounts.google.com/signin")
                time.sleep(3)

            # Handle email input
            email_selectors = ["#identifierId", "input[name='identifier']", "input[type='email']"]
            email_input = None

            for selector in email_selectors:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_input.is_displayed():
                        break
                except:
                    continue

            if not email_input:
                self._update_progress("❌ Email input field not found")
                return False

            # Fill email
            self._update_progress("📧 Entering email address...")
            email_input.clear()
            email_input.send_keys(email)
            time.sleep(1)

            # Click Next button
            next_selectors = ["#identifierNext", "button[type='submit']", ".VfPpkd-LgbsSe"]
            next_button = None

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_button.is_displayed():
                        break
                except:
                    continue

            if next_button:
                self._update_progress("➡️ Clicking Next...")
                next_button.click()
                time.sleep(3)
            else:
                # Try pressing Enter
                email_input.send_keys(Keys.RETURN)
                time.sleep(3)

            # Handle password input
            password_selectors = ["input[name='password']", "input[type='password']", "#password"]
            password_input = None

            # Wait for password page to load
            for attempt in range(10):
                for selector in password_selectors:
                    try:
                        password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if password_input.is_displayed():
                            break
                    except:
                        continue
                if password_input:
                    break
                time.sleep(1)

            if not password_input:
                self._update_progress("❌ Password input field not found")
                return False

            # Fill password
            self._update_progress("🔒 Entering password...")
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)

            # Click Next button for password
            password_next_selectors = ["#passwordNext", "button[type='submit']", ".VfPpkd-LgbsSe"]
            password_next = None

            for selector in password_next_selectors:
                try:
                    password_next = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_next.is_displayed():
                        break
                except:
                    continue

            if password_next:
                self._update_progress("➡️ Submitting login...")
                password_next.click()
            else:
                # Try pressing Enter
                password_input.send_keys(Keys.RETURN)

            # Wait for login to complete
            time.sleep(5)

            # Check if login was successful
            current_url = self.driver.current_url
            if "myactivity.google.com" in current_url or ("accounts.google.com" not in current_url and "google.com" in current_url):
                self._update_progress("✅ Successfully signed in!")

                # Navigate to YouTube comments page if not already there
                if "youtube_comments" not in current_url:
                    self._update_progress("🎯 Navigating to YouTube comments page...")
                    self.go_to_activity_page()
                    time.sleep(3)

                # Check for the specific "Your YouTube Comments" element
                try:
                    youtube_comments_element = self.driver.find_elements(By.XPATH,
                        "//div[@class='jPCT6' and text()='Your YouTube Comments']")

                    if youtube_comments_element:
                        self._update_progress("🎯 Found 'Your YouTube Comments' section - ready for deletion!")
                    else:
                        self._update_progress("🔍 Signed in successfully, waiting for comments page to load...")
                except Exception:
                    pass

                return True
            else:
                # Check for 2FA or other requirements
                if "challenge" in current_url or "verify" in current_url:
                    self._update_progress("🔐 2FA/verification required - please complete manually")
                    return True  # Consider this a success, user needs to complete 2FA
                else:
                    self._update_progress("❌ Sign-in may have failed - please check manually")
                    return False

        except Exception as e:
            self._update_progress(f"❌ Error during auto sign-in: {str(e)}")
            return False

    def check_youtube_comments_page(self, silent=False):
        """Specifically check for the 'Your YouTube Comments' element"""
        try:
            if not silent:
                self._update_progress("🔍 Checking for YouTube Comments page...")

            # Look for the specific "Your YouTube Comments" element
            youtube_comments_element = self.driver.find_elements(By.XPATH,
                "//div[@class='jPCT6' and text()='Your YouTube Comments']")

            if youtube_comments_element:
                if not silent:
                    self._update_progress("🎯 Found 'Your YouTube Comments' section!")
                    self._update_progress("✅ Ready for ultra-fast comment deletion!")
                return True
            else:
                # Check if we're on the right URL but content hasn't loaded
                current_url = self.driver.current_url
                if "myactivity.google.com" in current_url and "youtube_comments" in current_url:
                    if not silent:
                        self._update_progress("🔍 On YouTube comments page, but content still loading...")
                        self._update_progress("💡 Try refreshing the page or wait a moment")
                else:
                    if not silent:
                        self._update_progress("❌ Not on YouTube comments page")
                        self._update_progress("💡 Please navigate to YouTube comments in My Activity")

                return False

        except Exception as e:
            if not silent:
                self._update_progress(f"❌ Error checking YouTube comments page: {str(e)}")
            return False

    def click_at_coordinates(self, x, y):
        """Click at specific coordinates on the page with proper scaling"""
        if not self.driver:
            return False

        try:
            # Scale coordinates from display size to original browser size
            if hasattr(self, 'display_width') and hasattr(self, 'original_width'):
                scale_x = self.original_width / self.display_width
                scale_y = self.original_height / self.display_height

                actual_x = int(x * scale_x)
                actual_y = int(y * scale_y)

                # Ensure coordinates are within browser bounds
                actual_x = max(0, min(actual_x, self.original_width - 1))
                actual_y = max(0, min(actual_y, self.original_height - 1))
            else:
                actual_x, actual_y = x, y

            self._update_progress(f"🖱️ Clicking at display ({x}, {y}) → browser ({actual_x}, {actual_y}) [Browser: {self.original_width}x{self.original_height}]")

            # First, let's see what we're actually clicking on
            element_info = self.driver.execute_script(f"""
                var element = document.elementFromPoint({actual_x}, {actual_y});
                if (element) {{
                    return {{
                        tag: element.tagName,
                        id: element.id || 'no-id',
                        className: element.className || 'no-class',
                        text: element.textContent ? element.textContent.substring(0, 30) : 'no-text',
                        type: element.type || 'no-type',
                        clickable: element.onclick !== null || element.addEventListener !== undefined,
                        visible: element.offsetParent !== null,
                        rect: element.getBoundingClientRect()
                    }};
                }} else {{
                    return {{ error: 'No element found at coordinates' }};
                }}
            """)

            self._update_progress(f"🎯 Target element: {element_info}")

            # Multiple click strategies for maximum compatibility
            success = False

            # Strategy 1: Simple element click (most reliable)
            try:
                element = self.driver.execute_script(f"return document.elementFromPoint({actual_x}, {actual_y});")
                if element:
                    # Scroll element into view first
                    self.driver.execute_script("arguments[0].scrollIntoView({{behavior: 'instant', block: 'center'}});", element)
                    # Wait a moment for scroll
                    import time
                    time.sleep(0.2)
                    # Try direct element click
                    element.click()
                    self._update_progress(f"✅ Direct element click successful!")
                    success = True
                else:
                    self._update_progress(f"⚠️ No element found at coordinates ({actual_x}, {actual_y})")
            except Exception as e:
                self._update_progress(f"⚠️ Element click failed: {str(e)}")

            # Strategy 2: ActionChains click if element click failed
            if not success:
                try:
                    from selenium.webdriver.common.action_chains import ActionChains
                    # Move to element and click
                    element = self.driver.execute_script(f"return document.elementFromPoint({actual_x}, {actual_y});")
                    if element:
                        ActionChains(self.driver).move_to_element(element).click().perform()
                        self._update_progress(f"✅ ActionChains element click successful!")
                        success = True
                    else:
                        self._update_progress(f"⚠️ No element for ActionChains")
                except Exception as e:
                    self._update_progress(f"⚠️ ActionChains failed: {str(e)}")

            # Strategy 3: Simple JavaScript click as last resort
            if not success:
                try:
                    self.driver.execute_script(f"""
                        var element = document.elementFromPoint({actual_x}, {actual_y});
                        if (element) {{
                            element.click();
                        }}
                    """)
                    self._update_progress(f"✅ JavaScript click executed")
                    success = True
                except Exception as e:
                    self._update_progress(f"❌ JavaScript click failed: {str(e)}")

            if success:
                # Wait a moment for any page changes
                import time
                time.sleep(0.5)
                return True
            else:
                self._update_progress(f"❌ All click strategies failed for coordinates ({actual_x}, {actual_y})")
                return False

        except Exception as e:
            self._update_progress(f"❌ Click error: {str(e)}")
            return False

    def type_text(self, text):
        """Type text in the currently focused element with priority screenshot tracking"""
        if not self.driver:
            return False

        try:
            # Handle special keys
            if text == '\b':  # Backspace
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\b')
                self._update_progress("⌫ TYPE ACTION: Backspace")
                return True
            elif text == '\n':  # Enter
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\n')
                self._update_progress("↵ TYPE ACTION: Enter - May trigger page navigation")
                return True
            elif text == '\t':  # Tab
                active_element = self.driver.switch_to.active_element
                active_element.send_keys('\t')
                self._update_progress("⇥ TYPE ACTION: Tab")
                return True
            else:
                # Regular text - enhanced logging for important actions
                active_element = self.driver.switch_to.active_element
                active_element.send_keys(text)

                # Enhanced logging for typing actions
                if not hasattr(self, '_type_count'):
                    self._type_count = 0
                self._type_count += 1

                # Log more frequently for better action tracking
                if self._type_count % 3 == 0 or len(text) > 1 or text in [' ', '@', '.']:
                    self._update_progress(f"⌨️ TYPE ACTION: '{text}' (total chars: {self._type_count})")

                return True

        except Exception as e:
            self._update_progress(f"❌ TYPE ERROR: {str(e)}")
            return False

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.screenshot_enabled = False
            self.last_screenshot = None
