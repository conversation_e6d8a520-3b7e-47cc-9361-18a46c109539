import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import threading
from concurrent.futures import ThreadPoolExecutor

class UltraFastCommentDeleter:
    def __init__(self, progress_callback=None, refresh_interval=10, max_stuck_time=8):
        self.driver = None
        self.progress_callback = progress_callback
        self.deleted_count = 0
        self.target_speed = 4  # 4 deletes per second
        self.delete_interval = 1.0 / self.target_speed  # 0.25 seconds between deletes

        # Auto-refresh settings - configurable by user
        self.refresh_interval = refresh_interval  # User configurable refresh interval
        self.max_stuck_time = max_stuck_time     # User configurable stuck detection
        self.last_progress_time = time.time()
        self.last_deleted_count = 0

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def update_refresh_settings(self, refresh_interval=None, max_stuck_time=None):
        """Update refresh settings during runtime"""
        if refresh_interval is not None:
            self.refresh_interval = refresh_interval
            self._update_progress(f"🔄 Refresh interval updated to {refresh_interval}s")

        if max_stuck_time is not None:
            self.max_stuck_time = max_stuck_time
            self._update_progress(f"⏱️ Stuck detection updated to {max_stuck_time}s")

    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver optimized for speed"""
        try:
            chrome_options = ChromeOptions()

            # Performance optimizations
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")  # Faster loading
            chrome_options.add_argument("--disable-javascript")  # We'll enable selectively
            chrome_options.add_argument("--aggressive-cache-discard")
            chrome_options.add_argument("--memory-pressure-off")

            # Anti-detection (minimal)
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            if headless:
                chrome_options.add_argument("--headless")

            # Get ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set aggressive timeouts for speed
            self.driver.implicitly_wait(0.5)  # Very short implicit wait
            self.driver.set_page_load_timeout(10)  # Quick page load timeout

            self._update_progress("Ultra-fast browser started!")
            return True

        except Exception as e:
            self._update_progress(f"Failed to start browser: {str(e)}")
            return False

    def go_to_activity_page(self):
        """Navigate to YouTube comments page"""
        try:
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self._update_progress("Navigating to YouTube Comments page...")

            self.driver.get(url)

            # Quick wait for page load
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            self._update_progress("Comments page loaded. Please sign in if needed.")
            return True

        except Exception as e:
            self._update_progress(f"Failed to navigate: {str(e)}")
            return False

    def wait_for_login(self):
        """Quick login check"""
        self._update_progress("Checking login status...")

        try:
            # Quick check for logged-in state
            WebDriverWait(self.driver, 30).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("Login confirmed! Ready for ultra-fast deletion.")
            return True
        except TimeoutException:
            self._update_progress("Login timeout.")
            return False

    def _is_logged_in(self):
        """Quick login check"""
        try:
            # Look for any activity content
            indicators = self.driver.find_elements(By.CSS_SELECTOR, "div[data-ved], c-wiz")
            return len(indicators) > 0
        except:
            return False

    def ultra_fast_delete_all(self):
        """Ultra-fast deletion using JavaScript and optimized strategies with auto-refresh"""
        try:
            self._update_progress("🚀 Starting ULTRA-FAST deletion (4 deletes/second)...")
            self._update_progress(f"🔄 Auto-refresh enabled: every {self.refresh_interval}s or when stuck for {self.max_stuck_time}s")

            start_time = time.time()
            self.last_progress_time = start_time

            while True:
                cycle_start = time.time()
                initial_count = self.deleted_count

                # Method 1: Pure JavaScript batch deletion (fastest)
                deleted_js = self._javascript_batch_delete()

                if deleted_js > 0:
                    self._update_progress(f"JavaScript batch deleted {deleted_js} comments!")
                    self.deleted_count += deleted_js
                    self._update_progress_tracking()

                # Method 2: Optimized Selenium for remaining comments
                selenium_deleted = self._optimized_selenium_delete()

                # Check if we made progress this cycle
                cycle_deleted = self.deleted_count - initial_count

                if cycle_deleted == 0:
                    # No progress made, check if we need to refresh
                    if self._should_refresh():
                        self._refresh_page()
                        continue
                    else:
                        # No comments found and no need to refresh - we're done
                        break
                else:
                    self._update_progress_tracking()

                # Check for time-based refresh
                if time.time() - cycle_start > self.refresh_interval:
                    self._update_progress(f"⏰ {self.refresh_interval}s interval reached - refreshing page")
                    self._refresh_page()

            self._update_progress(f"🎉 Ultra-fast deletion complete! Total: {self.deleted_count}")

        except Exception as e:
            self._update_progress(f"Error during ultra-fast deletion: {str(e)}")

    def _should_refresh(self):
        """Check if page should be refreshed due to being stuck"""
        current_time = time.time()
        time_since_progress = current_time - self.last_progress_time

        if time_since_progress > self.max_stuck_time:
            self._update_progress(f"⚠️ No progress for {time_since_progress:.1f}s - page may be stuck")
            return True

        return False

    def _update_progress_tracking(self):
        """Update progress tracking variables"""
        self.last_progress_time = time.time()
        self.last_deleted_count = self.deleted_count

    def _refresh_page(self):
        """Refresh the page and wait for it to load"""
        try:
            self._update_progress("🔄 Refreshing page to continue deletion...")

            # Store current URL
            current_url = self.driver.current_url

            # Refresh the page
            self.driver.refresh()

            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Additional wait for dynamic content
            time.sleep(3)

            # Verify we're still on the right page
            if "myactivity.google.com" not in self.driver.current_url:
                self._update_progress("⚠️ Page changed after refresh, navigating back...")
                self.driver.get(current_url)
                time.sleep(3)

            self._update_progress("✅ Page refreshed successfully - continuing deletion")
            self._update_progress_tracking()  # Reset stuck timer

        except Exception as e:
            self._update_progress(f"❌ Error refreshing page: {str(e)}")
            # Try to navigate back to the comments page
            try:
                self.go_to_activity_page()
            except:
                pass

    def _javascript_batch_delete(self):
        """Use JavaScript for maximum speed deletion"""
        self._update_progress("🔥 Attempting JavaScript batch deletion...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        js_script = f"""
        // Ultra-fast JavaScript deletion
        console.log('Starting ultra-fast deletion...');

        // Hamburger menu path to avoid
        const hamburgerMenuPath = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z";

        // Find all X buttons with the specific path, avoiding hamburger menu
        const selectors = [
            'svg.TjcpUd path[d="{x_button_path}"]',
            'svg.NMm5M path[d="{x_button_path}"]',
            'path[d="{x_button_path}"]',
            'svg[width="24"][height="24"] path[d*="19 6.41"]'
        ];

        let allXButtons = [];
        selectors.forEach(selector => {{
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(path => {{
                // Skip if this is the hamburger menu
                if (path.getAttribute('d') === hamburgerMenuPath) {{
                    return;
                }}

                const button = path.closest('button');
                if (button && !allXButtons.includes(button)) {{
                    // Double-check the button doesn't contain hamburger menu
                    const hamburgerPaths = button.querySelectorAll(`path[d="${{hamburgerMenuPath}}"]`);
                    if (hamburgerPaths.length === 0) {{
                        allXButtons.push(button);
                    }}
                }}
            }});
        }});

        console.log('Found', allXButtons.length, 'delete buttons (excluding hamburger menus)');

        // Delete at 4 per second (250ms intervals)
        let deleted = 0;
        const deleteInterval = 250; // 250ms = 4 per second

        allXButtons.forEach((button, index) => {{
            setTimeout(() => {{
                try {{
                    // Click the delete button
                    button.click();
                    deleted++;
                    console.log('Deleted comment', deleted);

                    // Try to auto-confirm if confirmation dialog appears
                    setTimeout(() => {{
                        const confirmButtons = document.querySelectorAll('button');
                        confirmButtons.forEach(btn => {{
                            const text = btn.textContent.toLowerCase();
                            if (text.includes('delete') || text.includes('remove') || text.includes('confirm')) {{
                                btn.click();
                            }}
                        }});
                    }}, 50);

                }} catch (e) {{
                    console.error('Error deleting comment', index, e);
                }}
            }}, index * deleteInterval);
        }});

        // Return the number of buttons found
        return allXButtons.length;
        """

        try:
            result = self.driver.execute_script(js_script)
            return result or 0
        except Exception as e:
            self._update_progress(f"JavaScript deletion failed: {str(e)}")
            return 0

    def _optimized_selenium_delete(self):
        """Optimized Selenium deletion for remaining comments"""
        self._update_progress("🔧 Running optimized Selenium cleanup...")

        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        # Hamburger menu path to avoid
        hamburger_menu_path = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"

        # Ultra-fast selectors (most specific first), avoiding hamburger menu
        fast_selectors = [
            (By.CSS_SELECTOR, f'svg.TjcpUd path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, f'svg.NMm5M path[d="{x_button_path}"]'),
            (By.CSS_SELECTOR, 'svg[width="24"] path[d*="19 6.41"]'),
        ]

        start_time = time.time()
        deleted_this_round = 0

        while True:
            found_any = False
            round_start_count = self.deleted_count

            for by_method, selector in fast_selectors:
                try:
                    # Find elements quickly
                    elements = self.driver.find_elements(by_method, selector)

                    if elements:
                        found_any = True

                        # Process in batches for speed
                        batch_size = min(4, len(elements))  # 4 at a time for 4/second
                        batch = elements[:batch_size]

                        for path_element in batch:
                            try:
                                # Check if this path is the hamburger menu
                                path_d = path_element.get_attribute('d')
                                if path_d == hamburger_menu_path:
                                    continue  # Skip hamburger menu

                                # Find parent button quickly
                                button = path_element.find_element(By.XPATH, "./ancestor::button[1]")

                                # Double-check button doesn't contain hamburger menu
                                hamburger_paths = button.find_elements(By.CSS_SELECTOR, f'path[d="{hamburger_menu_path}"]')
                                if hamburger_paths:
                                    continue  # Skip if button contains hamburger menu

                                # Ultra-fast click
                                self.driver.execute_script("arguments[0].click();", button)

                                # Quick confirmation attempt
                                try:
                                    confirm_btn = WebDriverWait(self.driver, 0.1).until(
                                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Delete')]"))
                                    )
                                    self.driver.execute_script("arguments[0].click();", confirm_btn)
                                except TimeoutException:
                                    pass  # No confirmation needed

                                self.deleted_count += 1
                                deleted_this_round += 1
                                self._update_progress(f"⚡ Fast deleted #{self.deleted_count}")
                                self._update_progress_tracking()  # Update progress tracking

                                # Maintain 4/second speed
                                time.sleep(self.delete_interval)

                            except Exception as e:
                                continue

                        break  # Found elements with this selector, don't try others

                except Exception as e:
                    continue

            if not found_any:
                break

            # Check if we made progress this round
            round_deleted = self.deleted_count - round_start_count
            if round_deleted == 0:
                # No progress in this round, might be stuck
                break

            # Quick scroll to load more
            self.driver.execute_script("window.scrollBy(0, 1000);")
            time.sleep(0.5)  # Brief pause for loading

            # Safety timeout (don't run forever)
            if time.time() - start_time > 300:  # 5 minutes max
                self._update_progress("⏰ Safety timeout reached")
                break

        return deleted_this_round

    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
