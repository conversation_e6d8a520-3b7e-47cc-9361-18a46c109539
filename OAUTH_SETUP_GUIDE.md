# Google OAuth Setup Guide

## 🔐 Setting up Google OAuth for Cloud Deployment

This guide shows how to set up Google OAuth 2.0 authentication for the cloud-based YouTube Comment Deleter.

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create a new project** or select an existing one
3. **Enable the required APIs**:
   - Google+ API (for user info)
   - My Activity API (if available)
   - YouTube Data API v3

## Step 2: Create OAuth 2.0 Credentials

1. **Navigate to APIs & Services > Credentials**
2. **Click "Create Credentials" > "OAuth 2.0 Client IDs"**
3. **Configure the OAuth consent screen**:
   - Application name: "YouTube Comment Deleter"
   - User support email: Your email
   - Developer contact: Your email
   - Scopes: Add the required scopes

4. **Create OAuth 2.0 Client ID**:
   - Application type: **Web application**
   - Name: "YouTube Comment Deleter"
   - Authorized redirect URIs:
     - `http://localhost:5000/oauth/callback` (for local testing)
     - `https://yourdomain.com/oauth/callback` (for production)

## Step 3: Configure Environment Variables

### For Local Development:
```bash
export GOOGLE_CLIENT_ID="your-client-id.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-client-secret"
export GOOGLE_REDIRECT_URI="http://localhost:5000/oauth/callback"
```

### For Production:
```bash
export GOOGLE_CLIENT_ID="your-client-id.googleusercontent.com"
export GOOGLE_CLIENT_SECRET="your-client-secret"
export GOOGLE_REDIRECT_URI="https://yourdomain.com/oauth/callback"
```

### For Docker:
```yaml
# docker-compose.yml
environment:
  - GOOGLE_CLIENT_ID=your-client-id.googleusercontent.com
  - GOOGLE_CLIENT_SECRET=your-client-secret
  - GOOGLE_REDIRECT_URI=https://yourdomain.com/oauth/callback
```

## Step 4: Required OAuth Scopes

The application needs these scopes:
```python
SCOPES = [
    'openid',                                    # Basic OpenID
    'email',                                     # User email
    'profile',                                   # User profile info
    'https://www.googleapis.com/auth/activity'   # My Activity access
]
```

## Step 5: OAuth Flow Explanation

### How it works:
1. **User clicks "Sign in with Google"**
2. **Redirected to Google's OAuth page** (outside your platform)
3. **User authenticates with Google** (secure, official Google login)
4. **Google redirects back** to your platform with authorization code
5. **Your platform exchanges code** for access tokens
6. **Access tokens used** to make API calls on user's behalf

### Security Benefits:
- ✅ **User credentials never touch your servers**
- ✅ **Google handles all authentication**
- ✅ **Tokens can be revoked by user**
- ✅ **Follows OAuth 2.0 best practices**
- ✅ **Secure token exchange**

## Step 6: API Limitations & Alternatives

### Important Note:
Google doesn't provide a public API for My Activity data. The OAuth approach shown is conceptual. Here are practical alternatives:

### Alternative 1: Session Cookie Method
```python
# User provides their session cookies
# Use cookies to make authenticated requests to My Activity page
# Parse HTML to extract comment data
```

### Alternative 2: Browser Extension
```javascript
// Chrome extension runs in user's browser
// Has access to their logged-in session
// Communicates with your cloud API
```

### Alternative 3: Hybrid Approach
```python
# OAuth for user identification
# Guided manual process for comment deletion
# User performs actions in their own browser
```

## Step 7: Production Deployment

### Cloud Platform Configuration:

#### AWS:
```bash
# Set environment variables in ECS/Lambda
aws ecs put-secret-value --name google-oauth-credentials
```

#### Google Cloud:
```bash
# Use Secret Manager
gcloud secrets create google-oauth-client-id --data-file=client-id.txt
```

#### Azure:
```bash
# Use Key Vault
az keyvault secret set --vault-name MyKeyVault --name google-client-id
```

#### Heroku:
```bash
heroku config:set GOOGLE_CLIENT_ID=your-client-id
heroku config:set GOOGLE_CLIENT_SECRET=your-client-secret
```

## Step 8: Testing the OAuth Flow

### Local Testing:
1. **Start the application**: `python cloud_oauth_app.py`
2. **Open browser**: `http://localhost:5000`
3. **Click "Sign in with Google"**
4. **Complete OAuth flow**
5. **Verify user info appears**

### Production Testing:
1. **Deploy to your cloud platform**
2. **Update redirect URI** in Google Console
3. **Test OAuth flow** on production domain
4. **Verify SSL/HTTPS** is working

## Step 9: Error Handling

### Common Issues:
- **Invalid redirect URI**: Update in Google Console
- **Scope not approved**: Add scopes to OAuth consent screen
- **Token expired**: Implement refresh token logic
- **API quota exceeded**: Request quota increase

### Debug Tips:
```python
# Add logging to see OAuth responses
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Step 10: Security Best Practices

### Production Security:
- ✅ **Use HTTPS only**
- ✅ **Validate state parameter**
- ✅ **Store secrets securely**
- ✅ **Implement CSRF protection**
- ✅ **Use secure session cookies**
- ✅ **Implement token refresh**
- ✅ **Add rate limiting**

### Example Secure Configuration:
```python
app.config.update(
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)
)
```

## Ready for Cloud! 🚀

This OAuth approach is perfect for cloud deployment because:
- **Users authenticate outside your platform**
- **No browser automation needed**
- **Scalable and secure**
- **Works on any cloud provider**
- **Professional authentication flow**
