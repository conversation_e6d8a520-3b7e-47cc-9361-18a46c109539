#!/usr/bin/env python3
"""
Production-optimized server for Ultra-Fast YouTube Comment Deleter
Uses Gunicorn with eventlet workers for better performance
"""

import os
import sys
import multiprocessing
from gunicorn.app.base import BaseApplication
from ultra_fast_app import app, socketio

class GunicornApp(BaseApplication):
    """Custom Gunicorn application for SocketIO"""
    
    def __init__(self, app, options=None):
        self.options = options or {}
        self.application = app
        super().__init__()

    def load_config(self):
        config = {key: value for key, value in self.options.items()
                  if key in self.cfg.settings and value is not None}
        for key, value in config.items():
            self.cfg.set(key.lower(), value)

    def load(self):
        return self.application

def get_production_config():
    """Get optimized production configuration"""
    # Calculate optimal worker count
    cpu_count = multiprocessing.cpu_count()
    worker_count = min(cpu_count * 2 + 1, 8)  # Cap at 8 workers
    
    return {
        # Worker configuration
        'bind': '0.0.0.0:5000',
        'workers': 1,  # SocketIO requires single worker
        'worker_class': 'eventlet',  # Async worker for SocketIO
        'worker_connections': 1000,
        
        # Performance optimizations
        'keepalive': 5,
        'max_requests': 1000,
        'max_requests_jitter': 100,
        'preload_app': True,
        
        # Timeouts (optimized for local/fast connections)
        'timeout': 30,
        'graceful_timeout': 30,
        'keepalive_timeout': 5,
        
        # Logging
        'loglevel': 'info',
        'access_log_format': '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s',
        
        # Security
        'limit_request_line': 8192,
        'limit_request_fields': 100,
        'limit_request_field_size': 8190,
        
        # Memory optimization
        'max_requests_jitter': 50,
        'worker_tmp_dir': '/dev/shm' if os.path.exists('/dev/shm') else None,
    }

def run_production_server():
    """Run the production server with optimizations"""
    print("🚀 Starting Ultra-Fast YouTube Comment Deleter (Production Mode)")
    print("⚡ Optimized for maximum performance")
    
    config = get_production_config()
    
    print(f"🔧 Configuration:")
    print(f"   • Workers: {config['workers']} ({config['worker_class']})")
    print(f"   • Connections: {config['worker_connections']}")
    print(f"   • Bind: {config['bind']}")
    print(f"   • Timeout: {config['timeout']}s")
    print(f"   • Keepalive: {config['keepalive']}s")
    
    # Create and run Gunicorn app
    gunicorn_app = GunicornApp(app, config)
    gunicorn_app.run()

if __name__ == '__main__':
    # Check if gunicorn and eventlet are available
    try:
        import gunicorn
        import eventlet
        print("✅ Production dependencies available")
        run_production_server()
    except ImportError as e:
        print(f"❌ Production dependencies missing: {e}")
        print("📦 Install with: pip install gunicorn eventlet")
        print("🔄 Falling back to development server...")
        
        # Fallback to development server
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
