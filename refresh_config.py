"""
Auto-Refresh Configuration for YouTube Comment Deleter

Adjust these settings to control when the page refreshes automatically.
This helps prevent the deletion process from getting stuck on problematic comments.
"""

# Auto-refresh settings for different deletion modes

SIMPLE_MODE_CONFIG = {
    "refresh_interval": 120,    # Refresh every 2 minutes (conservative)
    "max_stuck_time": 45,       # Consider stuck after 45 seconds of no progress
    "description": "Conservative settings for simple comment deletion"
}

ULTRA_FAST_MODE_CONFIG = {
    "refresh_interval": 60,     # Refresh every 1 minute (aggressive)
    "max_stuck_time": 30,       # Consider stuck after 30 seconds of no progress
    "description": "Aggressive settings for ultra-fast deletion (4/second)"
}

BALANCED_MODE_CONFIG = {
    "refresh_interval": 90,     # Refresh every 1.5 minutes
    "max_stuck_time": 40,       # Consider stuck after 40 seconds of no progress
    "description": "Balanced settings for moderate speed deletion"
}

# Custom configuration - modify these values as needed
CUSTOM_CONFIG = {
    "refresh_interval": 120,    # Time in seconds between automatic page refreshes
    "max_stuck_time": 45,       # Time in seconds to wait before considering the process stuck
    "description": "Custom user-defined settings"
}

def get_config(mode="simple"):
    """Get configuration for specified mode"""
    configs = {
        "simple": SIMPLE_MODE_CONFIG,
        "ultra_fast": ULTRA_FAST_MODE_CONFIG,
        "balanced": BALANCED_MODE_CONFIG,
        "custom": CUSTOM_CONFIG
    }
    
    return configs.get(mode, SIMPLE_MODE_CONFIG)

def print_all_configs():
    """Print all available configurations"""
    print("🔄 Auto-Refresh Configuration Options:")
    print("=" * 50)
    
    configs = {
        "Simple Mode": SIMPLE_MODE_CONFIG,
        "Ultra-Fast Mode": ULTRA_FAST_MODE_CONFIG,
        "Balanced Mode": BALANCED_MODE_CONFIG,
        "Custom Mode": CUSTOM_CONFIG
    }
    
    for name, config in configs.items():
        print(f"\n{name}:")
        print(f"  Refresh Interval: {config['refresh_interval']}s")
        print(f"  Stuck Detection: {config['max_stuck_time']}s")
        print(f"  Description: {config['description']}")

# Recommended settings based on your needs:

RECOMMENDED_SETTINGS = """
🎯 RECOMMENDED SETTINGS:

1. CONSERVATIVE (Safest):
   - Refresh Interval: 180s (3 minutes)
   - Stuck Detection: 60s
   - Best for: First-time users, large comment counts

2. BALANCED (Recommended):
   - Refresh Interval: 120s (2 minutes)  
   - Stuck Detection: 45s
   - Best for: Most users, regular use

3. AGGRESSIVE (Fastest):
   - Refresh Interval: 60s (1 minute)
   - Stuck Detection: 30s
   - Best for: Experienced users, small comment counts

4. ULTRA-AGGRESSIVE (4 deletes/second):
   - Refresh Interval: 60s
   - Stuck Detection: 30s
   - Best for: Maximum speed, risk tolerance

⚠️ NOTES:
- Shorter intervals = faster recovery from stuck comments
- Longer intervals = less disruption to deletion process
- Stuck detection prevents infinite loops on problematic comments
- Page refresh resets the deletion state but continues from where it left off
"""

if __name__ == "__main__":
    print_all_configs()
    print(RECOMMENDED_SETTINGS)
