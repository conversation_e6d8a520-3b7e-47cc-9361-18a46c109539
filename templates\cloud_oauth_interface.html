<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Comment Deleter - Cloud Platform</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .auth-section {
            text-align: center;
            padding: 40px 20px;
        }
        
        .auth-section h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .auth-section p {
            margin-bottom: 30px;
            color: #666;
            font-size: 16px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn-primary { background: #4285f4; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .user-details {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .activities-section {
            margin-top: 30px;
        }
        
        .activity-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-date {
            color: #666;
            font-size: 14px;
        }
        
        .activity-text {
            margin-top: 5px;
            color: #333;
        }
        
        .progress-section {
            background: #e7f3ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: #856404;
        }
        
        .oauth-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .oauth-info h3 {
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .oauth-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .oauth-info li {
            margin: 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🌐 YouTube Comment Deleter</h1>
            <p>Cloud Platform - Secure OAuth Authentication</p>
        </header>

        <div class="content">
            {% if not authenticated %}
            <!-- Not Authenticated Section -->
            <div class="auth-section">
                <h2>🔐 Secure Authentication Required</h2>
                <p>To delete your YouTube comments, you need to authenticate with Google using OAuth 2.0.</p>
                
                <div class="oauth-info">
                    <h3>How it works:</h3>
                    <ul>
                        <li>✅ You'll be redirected to Google's official login page</li>
                        <li>✅ Sign in with your Google account securely</li>
                        <li>✅ Grant permission to access your YouTube activity</li>
                        <li>✅ Return to our platform to manage your comments</li>
                        <li>✅ Your credentials never touch our servers</li>
                    </ul>
                </div>
                
                <a href="/login" class="btn btn-primary">🔑 Sign in with Google</a>
            </div>
            
            {% else %}
            <!-- Authenticated Section -->
            <div class="user-info">
                <div class="user-details">
                    <img src="{{ session.user_info.picture }}" alt="Profile" class="user-avatar">
                    <div>
                        <strong>{{ session.user_info.name }}</strong><br>
                        <span class="user-email">{{ session.user_info.email }}</span>
                    </div>
                </div>
                <a href="/logout" class="btn btn-secondary">Logout</a>
            </div>

            <div class="warning">
                <h3>⚠️ Important Warning</h3>
                <p><strong>This will permanently delete ALL your YouTube comments.</strong></p>
                <p>This action cannot be undone!</p>
            </div>

            <div class="activities-section">
                <h3>Your YouTube Comments</h3>
                <p>Click "Load Comments" to see your YouTube comment activities.</p>
                
                <div style="margin: 20px 0;">
                    <button id="loadActivitiesBtn" class="btn btn-primary">📋 Load Comments</button>
                    <button id="deleteAllBtn" class="btn btn-danger" style="display: none;">🗑️ Delete All Comments</button>
                </div>

                <div class="progress-section" id="progressSection">
                    <h4>Deletion Progress</h4>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div id="progressText">Ready to start...</div>
                </div>

                <div id="activitiesList">
                    <!-- Activities will be loaded here -->
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        const socket = io();
        
        const loadActivitiesBtn = document.getElementById('loadActivitiesBtn');
        const deleteAllBtn = document.getElementById('deleteAllBtn');
        const activitiesList = document.getElementById('activitiesList');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        // Event listeners
        if (loadActivitiesBtn) {
            loadActivitiesBtn.addEventListener('click', loadActivities);
        }
        
        if (deleteAllBtn) {
            deleteAllBtn.addEventListener('click', deleteAllComments);
        }
        
        // Socket events
        socket.on('connect', () => {
            console.log('Connected to cloud server');
        });
        
        socket.on('activities_loaded', (data) => {
            displayActivities(data.items || []);
            loadActivitiesBtn.textContent = 'Comments Loaded ✓';
            deleteAllBtn.style.display = 'inline-block';
        });
        
        socket.on('activities_error', (data) => {
            activitiesList.innerHTML = `<p style="color: red;">Error loading activities: ${data.error}</p>`;
        });
        
        socket.on('deletion_progress', (data) => {
            progressSection.style.display = 'block';
            const percentage = (data.deleted / data.total) * 100;
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = data.message;
        });
        
        socket.on('deletion_complete', (data) => {
            progressFill.style.width = '100%';
            progressText.textContent = data.message;
            deleteAllBtn.textContent = 'Deletion Complete ✓';
            deleteAllBtn.disabled = true;
            
            // Reload activities to show updated list
            setTimeout(() => {
                loadActivities();
            }, 2000);
        });
        
        socket.on('deletion_error', (data) => {
            alert(`Deletion error: ${data.error}`);
        });
        
        socket.on('delete_success', (data) => {
            // Remove the deleted activity from the display
            const activityElement = document.querySelector(`[data-activity-id="${data.activity_id}"]`);
            if (activityElement) {
                activityElement.remove();
            }
        });
        
        // Functions
        function loadActivities() {
            loadActivitiesBtn.textContent = 'Loading...';
            loadActivitiesBtn.disabled = true;
            socket.emit('get_activities');
        }
        
        function deleteAllComments() {
            if (confirm('Are you absolutely sure you want to delete ALL your YouTube comments? This cannot be undone!')) {
                deleteAllBtn.textContent = 'Deleting...';
                deleteAllBtn.disabled = true;
                socket.emit('delete_all_comments');
            }
        }
        
        function deleteActivity(activityId) {
            if (confirm('Delete this comment?')) {
                socket.emit('delete_activity', { activity_id: activityId });
            }
        }
        
        function displayActivities(activities) {
            if (activities.length === 0) {
                activitiesList.innerHTML = '<p>No YouTube comment activities found.</p>';
                return;
            }
            
            let html = '<h4>Found ' + activities.length + ' comment activities:</h4>';
            
            activities.forEach(activity => {
                html += `
                    <div class="activity-item" data-activity-id="${activity.id}">
                        <div class="activity-content">
                            <div class="activity-date">${new Date(activity.time).toLocaleString()}</div>
                            <div class="activity-text">${activity.title || 'YouTube Comment'}</div>
                        </div>
                        <button class="btn btn-danger btn-sm" onclick="deleteActivity('${activity.id}')">Delete</button>
                    </div>
                `;
            });
            
            activitiesList.innerHTML = html;
        }
    </script>
</body>
</html>
