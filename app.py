from flask import Flask, render_template, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import threading
import time
from youtube_comment_deleter import YouTube<PERSON>ommentDeleter

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables
comment_deleter = None
deletion_thread = None

@app.route('/')
def index():
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('status', {'message': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('start_browser')
def handle_start_browser():
    global comment_deleter
    try:
        comment_deleter = YouTubeCommentDeleter(progress_callback=send_progress_update)
        comment_deleter.setup_driver(headless=False)
        
        # Start browser and navigate to YouTube
        if comment_deleter.login_to_youtube():
            emit('browser_ready', {'success': True})
        else:
            emit('browser_ready', {'success': False, 'error': 'Failed to setup browser'})
    except Exception as e:
        emit('browser_ready', {'success': False, 'error': str(e)})

@socketio.on('start_deletion')
def handle_start_deletion():
    global comment_deleter, deletion_thread
    
    if not comment_deleter:
        emit('deletion_status', {'error': 'Browser not initialized'})
        return
    
    if deletion_thread and deletion_thread.is_alive():
        emit('deletion_status', {'error': 'Deletion already in progress'})
        return
    
    def deletion_worker():
        try:
            comment_deleter.navigate_to_comments_history()
            comment_deleter.find_and_delete_comments()
        except Exception as e:
            send_progress_update(f"Error: {str(e)}")
    
    deletion_thread = threading.Thread(target=deletion_worker)
    deletion_thread.daemon = True
    deletion_thread.start()
    
    emit('deletion_status', {'started': True})

@socketio.on('stop_deletion')
def handle_stop_deletion():
    global comment_deleter
    if comment_deleter:
        comment_deleter.stop_deletion()
        emit('deletion_status', {'stopped': True})

@socketio.on('close_browser')
def handle_close_browser():
    global comment_deleter
    if comment_deleter:
        comment_deleter.cleanup()
        comment_deleter = None
        emit('browser_status', {'closed': True})

def send_progress_update(message):
    """Send progress update to all connected clients"""
    socketio.emit('progress_update', {'message': message})

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    print("Starting YouTube Comment Deleter Server...")
    print("Open your browser and go to: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
