import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from webdriver_manager.chrome import ChromeDriverManager
import random

class YouTubeCommentDeleter:
    def __init__(self, progress_callback=None):
        self.driver = None
        self.progress_callback = progress_callback
        self.is_running = False
        self.should_stop = False
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def login_to_youtube(self):
        """Navigate to YouTube and wait for manual login"""
        self.driver.get("https://www.youtube.com")
        self.driver.maximize_window()
        
        # Wait for user to manually log in
        self._update_progress("Please log in to YouTube manually. Click 'Start Deletion' when ready.")
        
        # Check if user is logged in by looking for profile picture or sign-in button
        try:
            WebDriverWait(self.driver, 300).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("Login detected. Ready to start deletion process.")
            return True
        except TimeoutException:
            self._update_progress("Login timeout. Please refresh and try again.")
            return False
    
    def _is_logged_in(self):
        """Check if user is logged in to YouTube"""
        try:
            # Look for avatar/profile picture which indicates logged in state
            self.driver.find_element(By.CSS_SELECTOR, "button[aria-label*='Account menu']")
            return True
        except NoSuchElementException:
            return False
    
    def navigate_to_comments_history(self):
        """Navigate to YouTube comment history"""
        try:
            # Go to YouTube Studio or My Comments page
            self.driver.get("https://myactivity.google.com/myactivity?product=YouTube")
            time.sleep(3)
            
            # Alternative: Go to YouTube history and filter for comments
            self.driver.get("https://www.youtube.com/feed/history")
            time.sleep(3)
            
            self._update_progress("Navigated to YouTube activity page.")
            return True
        except Exception as e:
            self._update_progress(f"Error navigating to comments: {str(e)}")
            return False
    
    def find_and_delete_comments(self):
        """Find and delete all comments"""
        self.is_running = True
        deleted_count = 0
        
        try:
            # Navigate to Google My Activity for YouTube
            self.driver.get("https://myactivity.google.com/myactivity?product=YouTube")
            time.sleep(5)
            
            # Look for comment activities
            while self.is_running and not self.should_stop:
                try:
                    # Find comment entries in My Activity
                    comment_elements = self.driver.find_elements(
                        By.XPATH, 
                        "//div[contains(text(), 'Commented on') or contains(text(), 'comment')]"
                    )
                    
                    if not comment_elements:
                        self._update_progress("No more comments found to delete.")
                        break
                    
                    for comment_element in comment_elements:
                        if self.should_stop:
                            break
                            
                        try:
                            # Find the delete button for this activity
                            parent_div = comment_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'activity')]")
                            delete_button = parent_div.find_element(By.XPATH, ".//button[@aria-label='Delete']")
                            
                            # Scroll to element and click
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", delete_button)
                            time.sleep(1)
                            delete_button.click()
                            
                            # Confirm deletion
                            confirm_button = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Delete') or contains(text(), 'Remove')]"))
                            )
                            confirm_button.click()
                            
                            deleted_count += 1
                            self._update_progress(f"Deleted comment #{deleted_count}")
                            
                            # Random delay to avoid rate limiting
                            time.sleep(random.uniform(2, 4))
                            
                        except (NoSuchElementException, TimeoutException, ElementClickInterceptedException) as e:
                            self.logger.warning(f"Could not delete comment: {str(e)}")
                            continue
                    
                    # Scroll down to load more activities
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)
                    
                except Exception as e:
                    self.logger.error(f"Error in deletion loop: {str(e)}")
                    break
            
            self._update_progress(f"Deletion process completed. Total comments deleted: {deleted_count}")
            
        except Exception as e:
            self._update_progress(f"Error during deletion process: {str(e)}")
        finally:
            self.is_running = False
    
    def stop_deletion(self):
        """Stop the deletion process"""
        self.should_stop = True
        self._update_progress("Stopping deletion process...")
    
    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
