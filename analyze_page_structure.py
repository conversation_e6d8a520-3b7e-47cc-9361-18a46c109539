#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze the YouTube comments page structure
This helps identify the exact selectors needed for comment deletion
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
import time
import json

def analyze_page_structure():
    """Analyze the structure of the YouTube comments page"""

    # Setup Chrome
    chrome_options = ChromeOptions()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver_path = ChromeDriverManager().install()
    service = ChromeService(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)

    try:
        print("🔍 Analyzing YouTube Comments Page Structure...")
        print("=" * 60)

        # Navigate to the comments page
        url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
        driver.get(url)
        driver.maximize_window()

        print("📍 Navigated to YouTube comments page")
        print("⏳ Please log in manually and wait for the page to load...")
        print("⏳ Press Enter when the comments are visible...")
        input()

        print("\n🔍 Analyzing page structure...")

        # Look for activity containers
        print("\n1. Looking for activity containers...")
        activity_selectors = [
            "div[data-ved]",
            "div[role='listitem']",
            "div.activity",
            "div[class*='activity']",
            "c-wiz",
            "div[jsname]"
        ]

        for selector in activity_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            print(f"   {selector}: {len(elements)} elements found")

            if elements and len(elements) > 0:
                print(f"   📋 Sample element HTML (first 200 chars):")
                print(f"   {elements[0].get_attribute('outerHTML')[:200]}...")

        # Look for X buttons specifically - the exact SVG structure
        print("\n2. Looking for X buttons (delete buttons)...")
        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        x_button_selectors = [
            # Exact SVG structure: <svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="TjcpUd NMm5M">
            f'svg[width="24"][height="24"][viewBox="0 0 24 24"] path[d="{x_button_path}"]',
            'svg.TjcpUd path[d*="19 6.41"]',
            'svg.NMm5M path[d*="19 6.41"]',
            'svg.TjcpUd.NMm5M path[d*="19 6.41"]',
            f"path[d='{x_button_path}']",
            "path[d*='19 6.41']",
            "svg path[d*='19 6.41']",
            "button svg path[d*='19 6.41']"
        ]

        for selector in x_button_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            print(f"   {selector}: {len(elements)} elements found")

            if elements:
                for i, element in enumerate(elements[:3]):  # Show first 3
                    parent_button = element.find_element(By.XPATH, "./ancestor::button[1]") if element else None
                    if parent_button:
                        print(f"   🎯 X button #{i+1} parent button:")
                        print(f"      Tag: {parent_button.tag_name}")
                        print(f"      Classes: {parent_button.get_attribute('class')}")
                        print(f"      Aria-label: {parent_button.get_attribute('aria-label')}")
                        print(f"      Data attributes: {[attr for attr in parent_button.get_attribute('outerHTML') if 'data-' in attr]}")

                        # Check the SVG element itself
                        svg_element = element.find_element(By.XPATH, "./ancestor::svg[1]") if element else None
                        if svg_element:
                            print(f"      SVG classes: {svg_element.get_attribute('class')}")
                            print(f"      SVG attributes: width={svg_element.get_attribute('width')}, height={svg_element.get_attribute('height')}")
                            print(f"      SVG viewBox: {svg_element.get_attribute('viewBox')}")
                            print(f"      SVG focusable: {svg_element.get_attribute('focusable')}")

        # Look for comment text elements
        print("\n3. Looking for comment text elements...")
        comment_text_selectors = [
            "div:contains('Commented on')",
            "span:contains('Commented on')",
            "div[class*='comment']",
            "span[class*='comment']"
        ]

        # Use JavaScript to find elements containing "Commented on"
        comment_elements = driver.execute_script("""
            var elements = [];
            var walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            var node;
            while (node = walker.nextNode()) {
                if (node.textContent.includes('Commented on') ||
                    node.textContent.includes('comment') ||
                    node.textContent.includes('Comment')) {
                    elements.push(node.parentElement);
                }
            }
            return elements.slice(0, 5);  // Return first 5
        """)

        print(f"   Found {len(comment_elements)} elements with comment-related text")

        for i, element in enumerate(comment_elements):
            print(f"   📝 Comment element #{i+1}:")
            print(f"      Tag: {element.tag_name}")
            print(f"      Text: {element.text[:100]}...")
            print(f"      Classes: {element.get_attribute('class')}")

        # Look for all buttons on the page
        print("\n4. Analyzing all buttons...")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"   Total buttons found: {len(all_buttons)}")

        # Filter buttons that might be delete buttons
        potential_delete_buttons = []
        for button in all_buttons:
            aria_label = button.get_attribute('aria-label') or ""
            button_text = button.text or ""

            if any(keyword in (aria_label + button_text).lower() for keyword in ['delete', 'remove', 'trash']):
                potential_delete_buttons.append(button)

        print(f"   Potential delete buttons: {len(potential_delete_buttons)}")

        for i, button in enumerate(potential_delete_buttons[:5]):  # Show first 5
            print(f"   🗑️ Delete button #{i+1}:")
            print(f"      Aria-label: {button.get_attribute('aria-label')}")
            print(f"      Text: {button.text}")
            print(f"      Classes: {button.get_attribute('class')}")

            # Check if it contains the X SVG
            svg_paths = button.find_elements(By.TAG_NAME, "path")
            for path in svg_paths:
                d_attr = path.get_attribute('d')
                if d_attr and '19 6.41' in d_attr:
                    print(f"      ✅ Contains X button SVG!")
                    print(f"      SVG path: {d_attr}")

        print("\n" + "=" * 60)
        print("🎯 Analysis complete!")
        print("📋 Use the information above to update the selectors in the code.")
        print("⏳ Press Enter to close the browser...")
        input()

    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")

    finally:
        driver.quit()

if __name__ == "__main__":
    analyze_page_structure()
