from flask import Flask, render_template, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import threading
import time
import os
from cloud_browser_manager import CloudBrowserManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-cloud-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables for cloud browser management
cloud_browser = None
deletion_thread = None
screenshot_thread = None

@app.route('/')
def index():
    return render_template('cloud_interface.html')

@app.route('/local')
def local_interface():
    """Fallback to local interface for development"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    print('Client connected to cloud interface')
    emit('status', {'message': 'Connected to cloud server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected from cloud interface')

@socketio.on('start_cloud_browser')
def handle_start_cloud_browser():
    global cloud_browser
    try:
        cloud_browser = CloudBrowserManager(progress_callback=send_progress_update)
        
        if cloud_browser.setup_cloud_browser():
            if cloud_browser.navigate_to_youtube():
                emit('cloud_browser_ready', {'success': True})
                send_progress_update("Cloud browser ready. Please log in to YouTube.")
            else:
                emit('cloud_browser_ready', {'success': False, 'error': 'Failed to navigate to YouTube'})
        else:
            emit('cloud_browser_ready', {'success': False, 'error': 'Failed to setup cloud browser'})
            
    except Exception as e:
        emit('cloud_browser_ready', {'success': False, 'error': str(e)})

@socketio.on('check_login_status')
def handle_check_login_status():
    global cloud_browser
    if cloud_browser:
        is_logged_in = cloud_browser.is_logged_in()
        emit('login_status', {'logged_in': is_logged_in})
        if is_logged_in:
            send_progress_update("Login detected! Ready to start deletion.")
        else:
            send_progress_update("Please log in to YouTube first.")
    else:
        emit('login_status', {'logged_in': False})

@socketio.on('get_screenshot')
def handle_get_screenshot():
    global cloud_browser
    if cloud_browser:
        screenshot = cloud_browser.get_current_screenshot()
        if screenshot:
            emit('screenshot_update', {'screenshot': screenshot})

@socketio.on('click_browser')
def handle_click_browser(data):
    global cloud_browser
    if cloud_browser:
        x, y = data['x'], data['y']
        cloud_browser.click_at_coordinates(x, y)

@socketio.on('type_text')
def handle_type_text(data):
    global cloud_browser
    if cloud_browser:
        text = data['text']
        cloud_browser.type_text(text)

@socketio.on('scroll_page')
def handle_scroll_page(data):
    global cloud_browser
    if cloud_browser:
        direction = data['direction']
        cloud_browser.scroll_page(direction)

@socketio.on('refresh_browser')
def handle_refresh_browser():
    global cloud_browser
    if cloud_browser:
        cloud_browser.driver.refresh()
        send_progress_update("Browser refreshed")

@socketio.on('start_cloud_deletion')
def handle_start_cloud_deletion():
    global cloud_browser, deletion_thread
    
    if not cloud_browser:
        emit('deletion_status', {'error': 'Cloud browser not initialized'})
        return
    
    if not cloud_browser.is_logged_in():
        emit('deletion_status', {'error': 'Please log in to YouTube first'})
        return
    
    if deletion_thread and deletion_thread.is_alive():
        emit('deletion_status', {'error': 'Deletion already in progress'})
        return
    
    def deletion_worker():
        try:
            cloud_browser.find_and_delete_comments_cloud()
        except Exception as e:
            send_progress_update(f"Error during deletion: {str(e)}")
    
    deletion_thread = threading.Thread(target=deletion_worker)
    deletion_thread.daemon = True
    deletion_thread.start()
    
    emit('deletion_status', {'started': True})

@socketio.on('stop_cloud_deletion')
def handle_stop_cloud_deletion():
    global cloud_browser
    if cloud_browser:
        cloud_browser.is_running = False
        emit('deletion_status', {'stopped': True})
        send_progress_update("Deletion process stopped by user")

@socketio.on('close_cloud_browser')
def handle_close_cloud_browser():
    global cloud_browser
    if cloud_browser:
        cloud_browser.cleanup()
        cloud_browser = None
        emit('browser_closed', {})

def send_progress_update(message):
    """Send progress update to all connected clients"""
    socketio.emit('progress_update', {'message': message})

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'mode': 'cloud',
        'browser_active': cloud_browser is not None
    })

@app.route('/api/status')
def api_status():
    """API endpoint for status checking"""
    return jsonify({
        'cloud_browser_running': cloud_browser is not None,
        'logged_in': cloud_browser.is_logged_in() if cloud_browser else False,
        'deletion_active': deletion_thread.is_alive() if deletion_thread else False
    })

# Cloud deployment configuration
def configure_for_cloud():
    """Configure application for cloud deployment"""
    # Set environment variables for cloud
    os.environ['DISPLAY'] = ':99'  # Virtual display
    
    # Additional cloud-specific configurations
    app.config['CLOUD_MODE'] = True
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload
    
    print("Application configured for cloud deployment")

if __name__ == '__main__':
    print("Starting YouTube Comment Deleter - Cloud Edition...")
    
    # Check if running in cloud environment
    if os.environ.get('CLOUD_DEPLOYMENT') == 'true':
        configure_for_cloud()
        print("Running in CLOUD mode")
        print("Access the application at: http://your-cloud-url/")
    else:
        print("Running in LOCAL mode")
        print("Cloud interface: http://localhost:5000")
        print("Local interface: http://localhost:5000/local")
    
    # Start the application
    socketio.run(
        app, 
        debug=False,  # Disable debug in cloud
        host='0.0.0.0', 
        port=int(os.environ.get('PORT', 5000)),
        allow_unsafe_werkzeug=True
    )
