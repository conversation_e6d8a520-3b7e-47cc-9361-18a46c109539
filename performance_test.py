#!/usr/bin/env python3
"""
Performance testing for YouTube comment deletion
Measures different bottlenecks and optimization strategies
"""

import time
import statistics
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager

class PerformanceTester:
    def __init__(self):
        self.driver = None
        self.timings = {}
    
    def setup_driver(self):
        """Setup Chrome with performance monitoring"""
        chrome_options = ChromeOptions()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        driver_path = ChromeDriverManager().install()
        service = ChromeService(driver_path)
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Enable performance logging
        self.driver.execute_cdp_cmd('Performance.enable', {})
    
    def measure_time(self, operation_name):
        """Decorator to measure operation time"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                duration = end_time - start_time
                if operation_name not in self.timings:
                    self.timings[operation_name] = []
                self.timings[operation_name].append(duration)
                
                print(f"⏱️ {operation_name}: {duration:.2f}s")
                return result
            return wrapper
        return decorator
    
    def test_page_load_performance(self):
        """Test page loading performance"""
        print("🔍 Testing Page Load Performance...")
        
        @self.measure_time("Page Load")
        def load_page():
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self.driver.get(url)
            time.sleep(3)  # Wait for initial load
        
        load_page()
    
    def test_element_finding_performance(self):
        """Test different selector performance"""
        print("\n🔍 Testing Element Finding Performance...")
        
        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
        
        selectors = {
            "Exact SVG": f'svg[width="24"][height="24"] path[d="{x_button_path}"]',
            "Class-based": 'svg.TjcpUd path[d*="19 6.41"]',
            "General path": 'path[d*="19 6.41"]',
            "Button aria": 'button[aria-label*="Delete"]',
            "XPath complex": f".//svg[@width='24']/path[@d='{x_button_path}']/ancestor::button[1]"
        }
        
        for name, selector in selectors.items():
            @self.measure_time(f"Find Elements - {name}")
            def find_elements():
                if selector.startswith(".//"):
                    return self.driver.find_elements(By.XPATH, selector)
                else:
                    return self.driver.find_elements(By.CSS_SELECTOR, selector)
            
            elements = find_elements()
            print(f"   Found {len(elements)} elements")
    
    def test_click_performance(self):
        """Test clicking performance with different methods"""
        print("\n🔍 Testing Click Performance...")
        
        # Find a button to test with
        buttons = self.driver.find_elements(By.TAG_NAME, "button")
        if not buttons:
            print("   No buttons found for testing")
            return
        
        test_button = buttons[0]
        
        @self.measure_time("Selenium Click")
        def selenium_click():
            # Don't actually click, just measure the setup time
            test_button.location_once_scrolled_into_view
        
        @self.measure_time("JavaScript Click")
        def javascript_click():
            self.driver.execute_script("arguments[0].scrollIntoView(true);", test_button)
        
        selenium_click()
        javascript_click()
    
    def test_delay_impact(self):
        """Test impact of different delay strategies"""
        print("\n🔍 Testing Delay Impact...")
        
        delay_strategies = {
            "No Delay": 0,
            "Fast (0.2s)": 0.2,
            "Medium (0.5s)": 0.5,
            "Current (2s)": 2.0,
            "Slow (5s)": 5.0
        }
        
        for name, delay in delay_strategies.items():
            @self.measure_time(f"Delay Strategy - {name}")
            def test_delay():
                time.sleep(delay)
                # Simulate finding an element
                self.driver.find_elements(By.TAG_NAME, "button")
            
            test_delay()
    
    def test_javascript_vs_selenium(self):
        """Compare JavaScript vs Selenium operations"""
        print("\n🔍 Testing JavaScript vs Selenium...")
        
        @self.measure_time("Selenium DOM Query")
        def selenium_query():
            return self.driver.find_elements(By.CSS_SELECTOR, "button")
        
        @self.measure_time("JavaScript DOM Query")
        def javascript_query():
            return self.driver.execute_script("return document.querySelectorAll('button').length;")
        
        selenium_elements = selenium_query()
        js_count = javascript_query()
        
        print(f"   Selenium found: {len(selenium_elements)} buttons")
        print(f"   JavaScript found: {js_count} buttons")
    
    def test_batch_vs_individual(self):
        """Test batch processing vs individual processing"""
        print("\n🔍 Testing Batch vs Individual Processing...")
        
        # Simulate finding multiple elements
        buttons = self.driver.find_elements(By.TAG_NAME, "button")[:10]  # Test with 10 buttons
        
        @self.measure_time("Individual Processing")
        def individual_processing():
            for button in buttons:
                button.location_once_scrolled_into_view  # Simulate processing
                time.sleep(0.1)  # Small delay
        
        @self.measure_time("Batch Processing")
        def batch_processing():
            # Process all at once with JavaScript
            self.driver.execute_script("""
                arguments[0].forEach(button => {
                    button.scrollIntoView();
                });
            """, buttons)
        
        individual_processing()
        batch_processing()
    
    def run_performance_tests(self):
        """Run all performance tests"""
        print("🚀 Starting Performance Tests...")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            # Navigate to page first
            print("⏳ Please log in manually and wait for comments to load...")
            print("⏳ Press Enter when ready to start performance tests...")
            input()
            
            self.test_element_finding_performance()
            self.test_click_performance()
            self.test_delay_impact()
            self.test_javascript_vs_selenium()
            self.test_batch_vs_individual()
            
            self.print_performance_summary()
            
        except Exception as e:
            print(f"❌ Error during testing: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def print_performance_summary(self):
        """Print performance summary"""
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE SUMMARY")
        print("=" * 60)
        
        for operation, times in self.timings.items():
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"{operation}:")
            print(f"   Average: {avg_time:.3f}s")
            print(f"   Min: {min_time:.3f}s")
            print(f"   Max: {max_time:.3f}s")
            print(f"   Samples: {len(times)}")
            print()
        
        # Recommendations
        print("🎯 RECOMMENDATIONS:")
        
        # Find fastest selector
        selector_times = {k: v for k, v in self.timings.items() if "Find Elements" in k}
        if selector_times:
            fastest_selector = min(selector_times.items(), key=lambda x: statistics.mean(x[1]))
            print(f"   ✅ Fastest selector: {fastest_selector[0]} ({statistics.mean(fastest_selector[1]):.3f}s)")
        
        # Delay recommendations
        delay_times = {k: v for k, v in self.timings.items() if "Delay Strategy" in k}
        if delay_times:
            fastest_delay = min(delay_times.items(), key=lambda x: statistics.mean(x[1]))
            print(f"   ✅ Optimal delay: {fastest_delay[0]} ({statistics.mean(fastest_delay[1]):.3f}s)")
        
        # Processing method
        if "Individual Processing" in self.timings and "Batch Processing" in self.timings:
            individual_avg = statistics.mean(self.timings["Individual Processing"])
            batch_avg = statistics.mean(self.timings["Batch Processing"])
            
            if batch_avg < individual_avg:
                speedup = individual_avg / batch_avg
                print(f"   ✅ Batch processing is {speedup:.1f}x faster than individual")
            else:
                print(f"   ⚠️ Individual processing is faster for this case")

if __name__ == "__main__":
    tester = PerformanceTester()
    tester.run_performance_tests()
