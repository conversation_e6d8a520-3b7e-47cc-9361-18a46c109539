#!/usr/bin/env python3
"""
Speed Diagnostic Tool for YouTube Comment Deletion
Measures performance degradation over time and identifies bottlenecks
"""

import time
import statistics
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager

class SpeedDiagnostic:
    def __init__(self):
        self.driver = None
        self.deletion_times = []
        self.element_find_times = []
        self.click_times = []
        self.page_load_times = []
        self.deletion_count = 0
        
    def setup_driver(self):
        """Setup Chrome for diagnostics"""
        chrome_options = ChromeOptions()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        driver_path = ChromeDriverManager().install()
        service = ChromeService(driver_path)
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Enable performance monitoring
        self.driver.execute_cdp_cmd('Performance.enable', {})
        
    def measure_deletion_cycle(self):
        """Measure one complete deletion cycle"""
        cycle_start = time.time()
        
        # 1. Measure element finding time
        find_start = time.time()
        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
        
        delete_buttons = self.driver.find_elements(By.CSS_SELECTOR, f'path[d="{x_button_path}"]')
        find_time = time.time() - find_start
        self.element_find_times.append(find_time)
        
        if not delete_buttons:
            return None
            
        # 2. Measure click time
        click_start = time.time()
        try:
            button = delete_buttons[0].find_element(By.XPATH, "./ancestor::button[1]")
            self.driver.execute_script("arguments[0].click();", button)
            
            # Try to confirm
            try:
                confirm_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Delete')]")
                self.driver.execute_script("arguments[0].click();", confirm_btn)
            except:
                pass
                
        except Exception as e:
            print(f"Click failed: {e}")
            return None
            
        click_time = time.time() - click_start
        self.click_times.append(click_time)
        
        # 3. Total cycle time
        total_time = time.time() - cycle_start
        self.deletion_times.append(total_time)
        self.deletion_count += 1
        
        return {
            'deletion_number': self.deletion_count,
            'total_time': total_time,
            'find_time': find_time,
            'click_time': click_time,
            'buttons_found': len(delete_buttons)
        }
    
    def get_page_performance(self):
        """Get browser performance metrics"""
        try:
            # Get performance metrics from browser
            performance = self.driver.execute_script("""
                return {
                    memory: performance.memory ? {
                        usedJSHeapSize: performance.memory.usedJSHeapSize,
                        totalJSHeapSize: performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                    } : null,
                    timing: performance.timing,
                    navigation: performance.navigation,
                    domElements: document.querySelectorAll('*').length,
                    buttons: document.querySelectorAll('button').length,
                    svgElements: document.querySelectorAll('svg').length
                };
            """)
            return performance
        except:
            return None
    
    def run_speed_test(self, max_deletions=20):
        """Run comprehensive speed test"""
        print("🔍 Starting Speed Diagnostic Test...")
        print("=" * 60)
        
        try:
            self.setup_driver()
            
            # Navigate to comments page
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self.driver.get(url)
            
            print("⏳ Please log in and wait for comments to load...")
            print("⏳ Press Enter when ready to start speed test...")
            input()
            
            print(f"\n🚀 Starting {max_deletions} deletion speed test...")
            
            # Baseline performance
            baseline_perf = self.get_page_performance()
            print(f"📊 Baseline - DOM Elements: {baseline_perf.get('domElements', 'N/A')}")
            print(f"📊 Baseline - Memory: {baseline_perf.get('memory', {}).get('usedJSHeapSize', 'N/A')} bytes")
            
            # Perform deletions and measure
            for i in range(max_deletions):
                print(f"\n--- Deletion #{i+1} ---")
                
                result = self.measure_deletion_cycle()
                if not result:
                    print("❌ No more delete buttons found")
                    break
                
                # Print real-time results
                print(f"⏱️ Total time: {result['total_time']:.3f}s")
                print(f"🔍 Find time: {result['find_time']:.3f}s")
                print(f"🖱️ Click time: {result['click_time']:.3f}s")
                print(f"🎯 Buttons found: {result['buttons_found']}")
                
                # Check performance every 5 deletions
                if (i + 1) % 5 == 0:
                    current_perf = self.get_page_performance()
                    if current_perf:
                        print(f"📊 Current - DOM Elements: {current_perf.get('domElements', 'N/A')}")
                        print(f"📊 Current - Memory: {current_perf.get('memory', {}).get('usedJSHeapSize', 'N/A')} bytes")
                
                # Calculate speed trend
                if len(self.deletion_times) >= 3:
                    recent_avg = statistics.mean(self.deletion_times[-3:])
                    overall_avg = statistics.mean(self.deletion_times)
                    trend = "📈 FASTER" if recent_avg < overall_avg else "📉 SLOWER"
                    print(f"📈 Speed trend: {trend} (Recent: {recent_avg:.3f}s vs Overall: {overall_avg:.3f}s)")
                
                # Wait between deletions (simulate real usage)
                time.sleep(0.5)
            
            self.print_analysis()
            
        except Exception as e:
            print(f"❌ Error during test: {e}")
        finally:
            if self.driver:
                print("\n⏳ Press Enter to close browser...")
                input()
                self.driver.quit()
    
    def print_analysis(self):
        """Print comprehensive analysis"""
        print("\n" + "=" * 60)
        print("📊 SPEED DEGRADATION ANALYSIS")
        print("=" * 60)
        
        if len(self.deletion_times) < 3:
            print("❌ Not enough data for analysis")
            return
        
        # Overall statistics
        print(f"\n📈 OVERALL STATISTICS:")
        print(f"   Total deletions: {len(self.deletion_times)}")
        print(f"   Average time: {statistics.mean(self.deletion_times):.3f}s")
        print(f"   Fastest deletion: {min(self.deletion_times):.3f}s")
        print(f"   Slowest deletion: {max(self.deletion_times):.3f}s")
        print(f"   Standard deviation: {statistics.stdev(self.deletion_times):.3f}s")
        
        # Speed trend analysis
        first_third = self.deletion_times[:len(self.deletion_times)//3]
        last_third = self.deletion_times[-len(self.deletion_times)//3:]
        
        first_avg = statistics.mean(first_third)
        last_avg = statistics.mean(last_third)
        
        print(f"\n📉 SPEED DEGRADATION:")
        print(f"   First third average: {first_avg:.3f}s")
        print(f"   Last third average: {last_avg:.3f}s")
        print(f"   Speed change: {((last_avg - first_avg) / first_avg * 100):+.1f}%")
        
        if last_avg > first_avg * 1.2:
            print("   🚨 SIGNIFICANT SLOWDOWN DETECTED!")
        elif last_avg > first_avg * 1.1:
            print("   ⚠️ Moderate slowdown detected")
        else:
            print("   ✅ Speed remained stable")
        
        # Component analysis
        print(f"\n🔍 COMPONENT BREAKDOWN:")
        print(f"   Average find time: {statistics.mean(self.element_find_times):.3f}s")
        print(f"   Average click time: {statistics.mean(self.click_times):.3f}s")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if last_avg > first_avg * 1.2:
            print("   🔄 Implement more frequent page refreshes")
            print("   ⚡ Reduce batch size to prevent rate limiting")
            print("   🎯 Add random delays to appear more human")
            print("   🧹 Clear browser cache between operations")
        
        if statistics.mean(self.element_find_times) > 0.1:
            print("   🎯 Optimize element selectors")
            print("   📋 Cache element references")
        
        if statistics.mean(self.click_times) > 0.2:
            print("   🖱️ Use JavaScript clicks instead of Selenium")
            print("   ⚡ Reduce confirmation wait times")

if __name__ == "__main__":
    diagnostic = SpeedDiagnostic()
    diagnostic.run_speed_test(max_deletions=15)
