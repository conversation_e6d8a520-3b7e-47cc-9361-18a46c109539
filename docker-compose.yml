version: '3.8'

services:
  youtube-comment-deleter:
    build: .
    ports:
      - "5000:5000"
    environment:
      - CLOUD_DEPLOYMENT=true
      - DISPLAY=:99
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    shm_size: 2gb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - youtube-comment-deleter
    restart: unless-stopped
    profiles:
      - production
