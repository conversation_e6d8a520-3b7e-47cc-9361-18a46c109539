* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 10px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    text-align: center;
}

header h1 {
    font-size: 2.2em;
    margin-bottom: 8px;
}

header p {
    font-size: 1em;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    padding: 20px;
    min-height: 600px;
}

.control-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    height: fit-content;
}

.step {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
    opacity: 0.6;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
    border-left-color: #28a745;
}

.step h3 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.step p {
    color: #666;
    margin-bottom: 12px;
    font-size: 0.9em;
}

.browser-container {
    background: #2c3e50;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.browser-header {
    background: #34495e;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.browser-header h3 {
    font-size: 1em;
    margin: 0;
}

.browser-controls {
    display: flex;
    gap: 8px;
}

.browser-viewport {
    flex: 1;
    position: relative;
    background: #ecf0f1;
    min-height: 500px;
    overflow: hidden;
}

.browser-viewport img {
    width: 100%;
    height: auto;
    display: block;
    cursor: crosshair;
}

.browser-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.browser-toolbar {
    background: #34495e;
    padding: 10px;
    display: flex;
    gap: 8px;
    align-items: center;
}

.browser-toolbar input {
    flex: 1;
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.login-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #e9ecef;
    padding: 8px 12px;
    border-radius: 5px;
    margin-top: 10px;
}

.deletion-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.progress-section {
    grid-column: 1 / -1;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    width: 0%;
    transition: width 0.3s ease;
}

.log-container {
    grid-column: 1 / -1;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.log-output {
    background: #000;
    color: #00ff00;
    padding: 15px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    height: 150px;
    overflow-y: auto;
    margin-bottom: 10px;
    font-size: 13px;
    line-height: 1.4;
}

.log-output p {
    margin-bottom: 3px;
}

.footer-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
}

.status-indicators {
    display: flex;
    gap: 15px;
}

.status-indicator {
    font-size: 12px;
    font-weight: 500;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-content h3 {
    color: #dc3545;
    margin-bottom: 15px;
}

.modal-content p {
    margin-bottom: 10px;
    color: #333;
}

.modal-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .browser-container {
        order: -1;
    }
}

@media (max-width: 768px) {
    .container {
        margin: 5px;
        border-radius: 10px;
    }
    
    header {
        padding: 15px;
    }
    
    header h1 {
        font-size: 1.8em;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .browser-toolbar {
        flex-wrap: wrap;
    }
    
    .browser-toolbar input {
        min-width: 200px;
    }
    
    .deletion-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
