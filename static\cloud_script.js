// Initialize Socket.IO connection
const socket = io();

// DOM elements
const startCloudBrowserBtn = document.getElementById('startCloudBrowserBtn');
const checkLoginBtn = document.getElementById('checkLoginBtn');
const startDeletionBtn = document.getElementById('startDeletionBtn');
const stopDeletionBtn = document.getElementById('stopDeletionBtn');
const closeBrowserBtn = document.getElementById('closeBrowserBtn');
const clearLogBtn = document.getElementById('clearLogBtn');
const refreshBtn = document.getElementById('refreshBtn');
const fullscreenBtn = document.getElementById('fullscreenBtn');
const typeBtn = document.getElementById('typeBtn');
const scrollUpBtn = document.getElementById('scrollUpBtn');
const scrollDownBtn = document.getElementById('scrollDownBtn');

const browserContainer = document.getElementById('browserContainer');
const browserScreen = document.getElementById('browserScreen');
const browserOverlay = document.getElementById('browserOverlay');
const textInput = document.getElementById('textInput');
const logOutput = document.getElementById('logOutput');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const loginStatus = document.getElementById('loginStatus');
const connectionStatus = document.getElementById('connectionStatus');
const browserStatus = document.getElementById('browserStatus');
const confirmModal = document.getElementById('confirmModal');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

// State variables
let isCloudBrowserRunning = false;
let isLoggedIn = false;
let isDeletionRunning = false;
let screenshotInterval = null;

// Event listeners
startCloudBrowserBtn.addEventListener('click', startCloudBrowser);
checkLoginBtn.addEventListener('click', checkLoginStatus);
startDeletionBtn.addEventListener('click', showConfirmModal);
stopDeletionBtn.addEventListener('click', stopDeletion);
closeBrowserBtn.addEventListener('click', closeCloudBrowser);
clearLogBtn.addEventListener('click', clearLog);
refreshBtn.addEventListener('click', refreshBrowser);
fullscreenBtn.addEventListener('click', toggleFullscreen);
typeBtn.addEventListener('click', typeText);
scrollUpBtn.addEventListener('click', () => scrollPage('up'));
scrollDownBtn.addEventListener('click', () => scrollPage('down'));
confirmDeleteBtn.addEventListener('click', confirmDeletion);
cancelDeleteBtn.addEventListener('click', hideConfirmModal);

// Browser interaction
browserScreen.addEventListener('click', handleBrowserClick);

// Socket event listeners
socket.on('connect', function() {
    addLog('Connected to cloud server');
    connectionStatus.textContent = '🟢 Connected';
});

socket.on('disconnect', function() {
    addLog('Disconnected from cloud server');
    connectionStatus.textContent = '🔴 Disconnected';
});

socket.on('cloud_browser_ready', function(data) {
    if (data.success) {
        isCloudBrowserRunning = true;
        browserStatus.textContent = '🟢 Browser Active';
        startCloudBrowserBtn.textContent = 'Browser Started ✓';
        startCloudBrowserBtn.disabled = true;
        
        showStep(2);
        browserContainer.style.display = 'block';
        
        // Start screenshot updates
        startScreenshotUpdates();
        
        addLog('Cloud browser started successfully');
    } else {
        addLog(`Error starting cloud browser: ${data.error}`);
        browserStatus.textContent = '🔴 Browser Error';
    }
});

socket.on('login_status', function(data) {
    isLoggedIn = data.logged_in;
    if (isLoggedIn) {
        loginStatus.textContent = 'Login successful ✓';
        loginStatus.style.color = '#28a745';
        showStep(3);
        addLog('YouTube login detected');
    } else {
        loginStatus.textContent = 'Not logged in';
        loginStatus.style.color = '#dc3545';
        addLog('Please log in to YouTube');
    }
});

socket.on('screenshot_update', function(data) {
    if (data.screenshot) {
        browserScreen.src = 'data:image/png;base64,' + data.screenshot;
    }
});

socket.on('deletion_status', function(data) {
    if (data.started) {
        isDeletionRunning = true;
        startDeletionBtn.style.display = 'none';
        stopDeletionBtn.style.display = 'inline-block';
        progressSection.style.display = 'block';
        addLog('Comment deletion started');
    } else if (data.stopped) {
        isDeletionRunning = false;
        startDeletionBtn.style.display = 'inline-block';
        stopDeletionBtn.style.display = 'none';
        addLog('Comment deletion stopped');
    } else if (data.error) {
        addLog(`Deletion error: ${data.error}`);
    }
});

socket.on('progress_update', function(data) {
    addLog(data.message);
    progressText.textContent = data.message;
    
    // Extract progress from message
    const match = data.message.match(/Deleted comment #(\d+)/);
    if (match) {
        const count = parseInt(match[1]);
        const progress = Math.min((count * 2), 100);
        progressFill.style.width = `${progress}%`;
    }
    
    if (data.message.includes('completed')) {
        progressFill.style.width = '100%';
        isDeletionRunning = false;
        startDeletionBtn.style.display = 'inline-block';
        stopDeletionBtn.style.display = 'none';
    }
});

socket.on('browser_closed', function() {
    isCloudBrowserRunning = false;
    browserStatus.textContent = '⚫ No Browser';
    startCloudBrowserBtn.textContent = 'Start Cloud Browser';
    startCloudBrowserBtn.disabled = false;
    browserContainer.style.display = 'none';
    hideStep(2);
    hideStep(3);
    stopScreenshotUpdates();
    addLog('Cloud browser session closed');
});

// Functions
function startCloudBrowser() {
    startCloudBrowserBtn.disabled = true;
    startCloudBrowserBtn.textContent = 'Starting...';
    addLog('Starting cloud browser...');
    socket.emit('start_cloud_browser');
}

function checkLoginStatus() {
    socket.emit('check_login_status');
    addLog('Checking login status...');
}

function showConfirmModal() {
    confirmModal.style.display = 'flex';
}

function hideConfirmModal() {
    confirmModal.style.display = 'none';
}

function confirmDeletion() {
    hideConfirmModal();
    socket.emit('start_cloud_deletion');
}

function stopDeletion() {
    socket.emit('stop_cloud_deletion');
}

function closeCloudBrowser() {
    socket.emit('close_cloud_browser');
}

function refreshBrowser() {
    socket.emit('refresh_browser');
    addLog('Refreshing browser...');
}

function toggleFullscreen() {
    if (browserContainer.requestFullscreen) {
        browserContainer.requestFullscreen();
    }
}

function typeText() {
    const text = textInput.value;
    if (text) {
        socket.emit('type_text', { text: text });
        textInput.value = '';
        addLog(`Typing: ${text}`);
    }
}

function scrollPage(direction) {
    socket.emit('scroll_page', { direction: direction });
    addLog(`Scrolling ${direction}`);
}

function handleBrowserClick(event) {
    if (!isCloudBrowserRunning) return;
    
    const rect = browserScreen.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Scale coordinates to actual browser size
    const scaleX = browserScreen.naturalWidth / rect.width;
    const scaleY = browserScreen.naturalHeight / rect.height;
    
    const actualX = Math.round(x * scaleX);
    const actualY = Math.round(y * scaleY);
    
    socket.emit('click_browser', { x: actualX, y: actualY });
    addLog(`Clicked at (${actualX}, ${actualY})`);
}

function startScreenshotUpdates() {
    screenshotInterval = setInterval(() => {
        if (isCloudBrowserRunning) {
            socket.emit('get_screenshot');
        }
    }, 2000);
}

function stopScreenshotUpdates() {
    if (screenshotInterval) {
        clearInterval(screenshotInterval);
        screenshotInterval = null;
    }
}

function clearLog() {
    logOutput.innerHTML = '<p>Log cleared.</p>';
}

function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('p');
    logEntry.textContent = `[${timestamp}] ${message}`;
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

function showStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.style.display = 'block';
        step.classList.add('active');
    }
}

function hideStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.style.display = 'none';
        step.classList.remove('active');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    addLog('YouTube Comment Deleter Cloud Edition loaded');
    addLog('Click "Start Cloud Browser" to begin');
});
