* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 30px;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.warning-box {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 10px;
    padding: 20px;
    margin: 20px;
    text-align: center;
}

.warning-box h3 {
    color: #856404;
    margin-bottom: 10px;
}

.warning-box p {
    color: #856404;
    margin-bottom: 5px;
}

.control-panel {
    padding: 20px;
}

.step {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.step h3 {
    color: #333;
    margin-bottom: 10px;
}

.step p {
    color: #666;
    margin-bottom: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin: 5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.status-indicator {
    background: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    font-weight: 500;
}

.progress-container {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    width: 0%;
    transition: width 0.3s ease;
}

#progressText {
    font-weight: 500;
    color: #333;
}

.log-container {
    margin: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.log-container h3 {
    margin-bottom: 15px;
    color: #333;
}

.log-output {
    background: #000;
    color: #00ff00;
    padding: 15px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.4;
}

.log-output p {
    margin-bottom: 5px;
}

.controls-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-content h3 {
    color: #dc3545;
    margin-bottom: 15px;
}

.modal-content p {
    margin-bottom: 10px;
    color: #333;
}

.modal-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    header {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
}
