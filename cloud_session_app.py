from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit
import requests
import json
import re
from bs4 import BeautifulSoup
import secrets
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
socketio = SocketIO(app, cors_allowed_origins="*")

class CloudSessionManager:
    def __init__(self):
        self.session_cookies = None
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

    def set_session_cookies(self, cookies_dict):
        """Set session cookies from user"""
        self.session_cookies = cookies_dict

    def get_comments_page(self):
        """Fetch the YouTube comments page using session cookies"""
        if not self.session_cookies:
            return {"error": "No session cookies provided"}

        headers = {
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"

        try:
            response = requests.get(url, headers=headers, cookies=self.session_cookies)

            if response.status_code == 200:
                return {"success": True, "content": response.text}
            else:
                return {"error": f"Failed to fetch page: {response.status_code}"}

        except Exception as e:
            return {"error": f"Request failed: {str(e)}"}

    def parse_comments(self, html_content):
        """Parse comments from the HTML content"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Look for comment activities (this would need to be adjusted based on actual HTML structure)
            comments = []

            # Find activity items - these selectors would need to be updated based on actual page structure
            activity_items = soup.find_all('div', class_='activity-item') or soup.find_all('div', {'data-ved': True})

            for item in activity_items:
                # Extract comment information
                comment_data = {
                    'id': item.get('data-ved') or str(hash(str(item))),
                    'text': self._extract_comment_text(item),
                    'date': self._extract_date(item),
                    'video_title': self._extract_video_title(item),
                    'delete_url': self._extract_delete_url(item)
                }

                if comment_data['text']:  # Only add if we found comment text
                    comments.append(comment_data)

            return {"success": True, "comments": comments}

        except Exception as e:
            return {"error": f"Failed to parse comments: {str(e)}"}

    def _extract_comment_text(self, item):
        """Extract comment text from activity item"""
        # Look for various possible selectors for comment text
        text_selectors = [
            '.comment-text',
            '.activity-description',
            '[data-comment]',
            'span:contains("Commented")',
        ]

        for selector in text_selectors:
            element = item.select_one(selector)
            if element:
                return element.get_text(strip=True)

        return item.get_text(strip=True)[:100] + "..." if len(item.get_text(strip=True)) > 100 else item.get_text(strip=True)

    def _extract_date(self, item):
        """Extract date from activity item"""
        # Look for date elements
        date_selectors = [
            '.activity-date',
            '.timestamp',
            'time',
            '[datetime]'
        ]

        for selector in date_selectors:
            element = item.select_one(selector)
            if element:
                return element.get_text(strip=True) or element.get('datetime')

        return "Unknown date"

    def _extract_video_title(self, item):
        """Extract video title from activity item"""
        # Look for video title elements
        title_selectors = [
            '.video-title',
            '.activity-title',
            'a[href*="youtube.com/watch"]'
        ]

        for selector in title_selectors:
            element = item.select_one(selector)
            if element:
                return element.get_text(strip=True)

        return "Unknown video"

    def _extract_delete_url(self, item):
        """Extract delete URL or button from activity item"""
        # Look for the specific X button with the complete SVG structure
        x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"

        # Look for delete buttons targeting the exact SVG structure
        delete_selectors = [
            # Target the exact SVG structure: <svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="TjcpUd NMm5M">
            f'svg[width="24"][height="24"][viewBox="0 0 24 24"] path[d="{x_button_path}"]',
            f'svg.TjcpUd path[d="{x_button_path}"]',
            f'svg.NMm5M path[d="{x_button_path}"]',
            f'svg.TjcpUd.NMm5M path[d="{x_button_path}"]',
            f'path[d="{x_button_path}"]',  # Direct path match

            # More general patterns for the X button
            'svg[width="24"][height="24"][viewBox="0 0 24 24"] path[d*="19 6.41"]',
            'svg.TjcpUd path[d*="19 6.41"]',
            'svg.NMm5M path[d*="19 6.41"]',

            # Traditional delete button selectors
            'button[aria-label*="Delete"]',
            'button[aria-label*="Remove"]',
            '[data-delete-url]',
            '.delete-button',

            # Button containing the X SVG
            'button svg[width="24"][height="24"] path[d*="19 6.41"]',
            'button svg.TjcpUd path[d*="19 6.41"]',
            'button svg.NMm5M path[d*="19 6.41"]'
        ]

        for selector in delete_selectors:
            try:
                element = item.select_one(selector)
                if element:
                    # If we found the SVG path, get the parent button
                    if element.name == 'path':
                        button = element.find_parent('button')
                        if button:
                            return {
                                'type': 'button',
                                'element': str(button),
                                'onclick': button.get('onclick'),
                                'data_params': self._extract_button_data(button)
                            }
                    else:
                        return {
                            'type': 'element',
                            'element': str(element),
                            'href': element.get('href'),
                            'onclick': element.get('onclick'),
                            'data_params': self._extract_button_data(element)
                        }
            except Exception as e:
                continue

        return None

    def _extract_button_data(self, button_element):
        """Extract data attributes and parameters from button element"""
        data = {}

        # Extract all data-* attributes
        for attr_name, attr_value in button_element.attrs.items():
            if attr_name.startswith('data-'):
                data[attr_name] = attr_value

        # Look for common Google activity parameters
        common_params = ['data-ved', 'data-token', 'data-activity-id', 'jsaction', 'jsname']
        for param in common_params:
            if button_element.get(param):
                data[param] = button_element.get(param)

        return data

    def delete_comment(self, delete_info):
        """Delete a comment using the provided delete information"""
        if not self.session_cookies:
            return {"error": "No session cookies provided"}

        # This would need to be implemented based on the actual delete mechanism
        # It might involve POST requests to specific endpoints with CSRF tokens

        headers = {
            'User-Agent': self.user_agent,
            'Referer': 'https://myactivity.google.com/',
            'X-Requested-With': 'XMLHttpRequest',
        }

        try:
            # Placeholder for actual delete implementation
            # This would need to be reverse-engineered from the actual page

            return {"success": True, "message": "Comment deletion simulated"}

        except Exception as e:
            return {"error": f"Delete failed: {str(e)}"}

# Global session manager
session_manager = CloudSessionManager()

@app.route('/')
def index():
    """Main page"""
    return render_template('cloud_session_interface.html')

@app.route('/upload_cookies', methods=['POST'])
def upload_cookies():
    """Handle cookie upload from user"""
    try:
        cookies_data = request.json.get('cookies')

        if not cookies_data:
            return jsonify({'error': 'No cookies provided'}), 400

        # Convert cookies to dict format
        cookies_dict = {}

        if isinstance(cookies_data, str):
            # Parse cookie string format
            for cookie in cookies_data.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies_dict[key] = value
        elif isinstance(cookies_data, list):
            # Parse cookie array format (from browser extension)
            for cookie in cookies_data:
                if 'name' in cookie and 'value' in cookie:
                    cookies_dict[cookie['name']] = cookie['value']

        session_manager.set_session_cookies(cookies_dict)
        session['cookies_uploaded'] = True

        return jsonify({'success': True, 'message': 'Cookies uploaded successfully'})

    except Exception as e:
        return jsonify({'error': f'Failed to upload cookies: {str(e)}'}), 500

@socketio.on('connect')
def handle_connect():
    print('Client connected to cloud session interface')
    emit('status', {'message': 'Connected to cloud server'})

@socketio.on('load_comments')
def handle_load_comments():
    """Load comments using session cookies"""
    if not session.get('cookies_uploaded'):
        emit('comments_error', {'error': 'No session cookies uploaded'})
        return

    # Fetch the comments page
    result = session_manager.get_comments_page()

    if 'error' in result:
        emit('comments_error', result)
        return

    # Parse comments from the page
    comments_result = session_manager.parse_comments(result['content'])

    if 'error' in comments_result:
        emit('comments_error', comments_result)
    else:
        emit('comments_loaded', comments_result)

@socketio.on('delete_comment')
def handle_delete_comment(data):
    """Delete a specific comment"""
    if not session.get('cookies_uploaded'):
        emit('delete_error', {'error': 'No session cookies uploaded'})
        return

    comment_id = data.get('comment_id')
    delete_info = data.get('delete_info')

    result = session_manager.delete_comment(delete_info)

    if 'error' in result:
        emit('delete_error', result)
    else:
        emit('delete_success', {'comment_id': comment_id})

@socketio.on('delete_all_comments')
def handle_delete_all_comments():
    """Delete all comments"""
    if not session.get('cookies_uploaded'):
        emit('deletion_error', {'error': 'No session cookies uploaded'})
        return

    # First load all comments
    result = session_manager.get_comments_page()

    if 'error' in result:
        emit('deletion_error', result)
        return

    comments_result = session_manager.parse_comments(result['content'])

    if 'error' in comments_result:
        emit('deletion_error', comments_result)
        return

    comments = comments_result['comments']
    total_count = len(comments)
    deleted_count = 0

    emit('deletion_started', {'total': total_count})

    for comment in comments:
        result = session_manager.delete_comment(comment.get('delete_url'))

        if 'error' not in result:
            deleted_count += 1

        emit('deletion_progress', {
            'deleted': deleted_count,
            'total': total_count,
            'message': f'Deleted {deleted_count} of {total_count} comments'
        })

        time.sleep(1)  # Rate limiting

    emit('deletion_complete', {
        'deleted': deleted_count,
        'total': total_count,
        'message': f'Deletion complete! Deleted {deleted_count} comments.'
    })

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'version': 'cloud_session',
        'cookies_uploaded': session.get('cookies_uploaded', False)
    })

if __name__ == '__main__':
    print("Starting YouTube Comment Deleter - Cloud Session Edition...")
    print("This version uses session cookies for authentication")
    print("Users will need to provide their browser session cookies")
    print("")
    print("Access the application at: http://localhost:5000")

    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
