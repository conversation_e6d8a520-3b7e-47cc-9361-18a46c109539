from flask import Flask, render_template, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import threading
import time
from ultra_fast_deleter import UltraFastCommentDeleter

app = Flask(__name__)
app.config['SECRET_KEY'] = 'ultra-fast-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables
ultra_deleter = None
deletion_thread = None
screenshot_thread = None
screenshot_active = False

# Default refresh settings
default_refresh_interval = 10  # 10 seconds
default_stuck_time = 8         # 8 seconds

@app.route('/')
def index():
    return render_template('ultra_fast_interface.html')

@socketio.on('connect')
def handle_connect():
    print('Client connected to ultra-fast interface')

    # Check if browser is already running and send current state
    global ultra_deleter
    if ultra_deleter and ultra_deleter.driver:
        try:
            # <PERSON><PERSON><PERSON> is running, send current state
            screenshot_data = ultra_deleter.get_browser_screenshot()
            if screenshot_data:
                emit('browser_ready', {
                    'success': True,
                    'interactive_mode': True,
                    'screenshot': screenshot_data.get('screenshot'),
                    'refresh_interval': ultra_deleter.refresh_interval,
                    'stuck_time': ultra_deleter.max_stuck_time,
                    'reconnected': True
                })
                emit('browser_screenshot', screenshot_data)
                print("🔄 Reconnected client to existing browser session")
            else:
                emit('browser_ready', {
                    'success': True,
                    'interactive_mode': True,
                    'refresh_interval': ultra_deleter.refresh_interval,
                    'stuck_time': ultra_deleter.max_stuck_time,
                    'reconnected': True
                })
                print("🔄 Reconnected client to existing browser session (no screenshot)")
        except Exception as e:
            print(f"❌ Error reconnecting to browser: {e}")
            # Browser might be dead, clean up
            ultra_deleter = None
            emit('browser_status', {'closed': True})

    emit('status', {'message': 'Connected to ultra-fast server'})

@socketio.on('start_browser')
def handle_start_browser(data=None):
    global ultra_deleter
    try:
        # Get user settings or use defaults
        refresh_interval = default_refresh_interval
        stuck_time = default_stuck_time

        if data:
            refresh_interval = data.get('refresh_interval', default_refresh_interval)
            stuck_time = data.get('stuck_time', default_stuck_time)

        ultra_deleter = UltraFastCommentDeleter(
            progress_callback=send_progress_update,
            refresh_interval=refresh_interval,
            max_stuck_time=stuck_time
        )

        if ultra_deleter.setup_driver(headless=False):
            if ultra_deleter.go_to_activity_page():
                # Enable interactive mode and get initial screenshot
                ultra_deleter.enable_interactive_mode()
                screenshot_data = ultra_deleter.get_browser_screenshot()

                # Extract just the screenshot for browser_ready event
                screenshot = screenshot_data.get('screenshot') if screenshot_data else None

                emit('browser_ready', {
                    'success': True,
                    'refresh_interval': refresh_interval,
                    'stuck_time': stuck_time,
                    'screenshot': screenshot,
                    'interactive_mode': True
                })

                # Send full screenshot data for browser display
                if screenshot_data:
                    emit('browser_screenshot', screenshot_data)

                # Start periodic screenshot updates immediately
                start_screenshot_updates()

                # Send an immediate second screenshot to ensure UI is responsive
                def send_immediate_screenshot():
                    time.sleep(0.5)  # Brief delay for page to settle
                    screenshot_data = ultra_deleter.get_browser_screenshot()
                    if screenshot_data:
                        socketio.emit('browser_screenshot', screenshot_data)

                immediate_thread = threading.Thread(target=send_immediate_screenshot)
                immediate_thread.daemon = True
                immediate_thread.start()
            else:
                emit('browser_ready', {'success': False, 'error': 'Failed to navigate'})
        else:
            emit('browser_ready', {'success': False, 'error': 'Failed to setup browser'})
    except Exception as e:
        emit('browser_ready', {'success': False, 'error': str(e)})

@socketio.on('update_refresh_settings')
def handle_update_refresh_settings(data):
    global ultra_deleter
    if ultra_deleter:
        refresh_interval = data.get('refresh_interval')
        stuck_time = data.get('stuck_time')

        ultra_deleter.update_refresh_settings(refresh_interval, stuck_time)
        emit('settings_updated', {
            'refresh_interval': ultra_deleter.refresh_interval,
            'stuck_time': ultra_deleter.max_stuck_time
        })
    else:
        emit('settings_error', {'error': 'Browser not started'})

@socketio.on('check_login')
def handle_check_login():
    global ultra_deleter
    if ultra_deleter:
        if ultra_deleter.wait_for_login():
            emit('login_ready', {'success': True})
        else:
            emit('login_ready', {'success': False})
    else:
        emit('login_ready', {'success': False, 'error': 'Browser not started'})



@socketio.on('start_ultra_fast_deletion')
def handle_start_ultra_fast_deletion():
    global ultra_deleter, deletion_thread

    if not ultra_deleter:
        emit('deletion_status', {'error': 'Browser not initialized'})
        return

    if deletion_thread and deletion_thread.is_alive():
        emit('deletion_status', {'error': 'Deletion already in progress'})
        return

    def ultra_deletion_worker():
        try:
            ultra_deleter.ultra_fast_delete_all()
        except Exception as e:
            send_progress_update(f"Error: {str(e)}")

    deletion_thread = threading.Thread(target=ultra_deletion_worker)
    deletion_thread.daemon = True
    deletion_thread.start()

    emit('deletion_status', {'started': True})

@socketio.on('close_browser')
def handle_close_browser():
    global ultra_deleter
    if ultra_deleter:
        stop_screenshot_updates()
        ultra_deleter.cleanup()
        ultra_deleter = None
        emit('browser_status', {'closed': True})

def start_screenshot_updates():
    """Start periodic screenshot updates"""
    global screenshot_thread, screenshot_active

    if screenshot_thread and screenshot_thread.is_alive():
        return

    screenshot_active = True

    def screenshot_worker():
        while screenshot_active and ultra_deleter:
            try:
                screenshot_data = ultra_deleter.get_browser_screenshot()
                if screenshot_data:
                    # Check if this is a priority screenshot (URL change or action)
                    if screenshot_data.get('url_changed') or screenshot_data.get('priority_reason'):
                        # Send priority screenshots immediately
                        socketio.emit('browser_screenshot', screenshot_data)
                        print(f"📸 Priority screenshot sent: {screenshot_data.get('priority_reason', 'url_change')}")
                    else:
                        # Regular screenshots
                        socketio.emit('browser_screenshot', screenshot_data)

                # Faster updates for better responsiveness
                time.sleep(0.2)  # Update every 200ms for even better responsiveness
            except Exception as e:
                print(f"Screenshot error: {e}")
                break

    screenshot_thread = threading.Thread(target=screenshot_worker)
    screenshot_thread.daemon = True
    screenshot_thread.start()

def stop_screenshot_updates():
    """Stop screenshot updates"""
    global screenshot_active
    screenshot_active = False

@socketio.on('browser_click')
def handle_browser_click(data):
    """Handle user clicks on the browser screenshot with priority response"""
    global ultra_deleter
    if ultra_deleter:
        x = data.get('x', 0)
        y = data.get('y', 0)
        success = ultra_deleter.click_at_coordinates(x, y)
        emit('click_result', {'success': success, 'x': x, 'y': y})

        # Send immediate priority screenshot update after click
        def send_priority_click_screenshot():
            time.sleep(0.1)  # Very brief delay for click to register
            screenshot_data = ultra_deleter.get_browser_screenshot(priority_reason='click')
            if screenshot_data:
                socketio.emit('browser_screenshot', screenshot_data)
                print(f"🖱️ Click screenshot sent for coordinates ({x}, {y})")

        click_thread = threading.Thread(target=send_priority_click_screenshot)
        click_thread.daemon = True
        click_thread.start()

@socketio.on('browser_type')
def handle_browser_type(data):
    """Handle user typing in the browser with priority response"""
    global ultra_deleter
    if ultra_deleter:
        text = data.get('text', '')
        success = ultra_deleter.type_text(text)
        emit('type_result', {'success': success, 'text': text})

        # Send priority screenshot update for important typing actions
        if text in ['\n', '\t'] or len(text) > 1:  # Enter, Tab, or multi-character input
            def send_priority_type_screenshot():
                time.sleep(0.1)  # Very brief delay for typing to register
                screenshot_data = ultra_deleter.get_browser_screenshot(priority_reason='type')
                if screenshot_data:
                    socketio.emit('browser_screenshot', screenshot_data)
                    print(f"⌨️ Type screenshot sent for: '{text}'")

            type_thread = threading.Thread(target=send_priority_type_screenshot)
            type_thread.daemon = True
            type_thread.start()
        elif text in [' ', '@', '.']:  # Important single characters
            def send_quick_type_screenshot():
                time.sleep(0.05)  # Even quicker for single chars
                screenshot_data = ultra_deleter.get_browser_screenshot(priority_reason='type')
                if screenshot_data:
                    socketio.emit('browser_screenshot', screenshot_data)

            quick_type_thread = threading.Thread(target=send_quick_type_screenshot)
            quick_type_thread.daemon = True
            quick_type_thread.start()

@socketio.on('get_screenshot')
def handle_get_screenshot():
    """Get current browser screenshot on demand"""
    global ultra_deleter
    if ultra_deleter:
        screenshot_data = ultra_deleter.get_browser_screenshot()
        if screenshot_data:
            emit('browser_screenshot', screenshot_data)

@socketio.on('find_signin')
def handle_find_signin():
    """Find and highlight the sign-in button"""
    global ultra_deleter
    if ultra_deleter:
        element = ultra_deleter.find_and_highlight_signin()
        emit('signin_found', {'success': element is not None})

@socketio.on('auto_signin')
def handle_auto_signin():
    """Automatically click the sign-in button"""
    global ultra_deleter
    if ultra_deleter:
        success = ultra_deleter.auto_click_signin()
        emit('signin_clicked', {'success': success})

        # Force screenshot update after sign-in click
        if success:
            import time
            time.sleep(2)  # Wait for page to load
            screenshot_data = ultra_deleter.get_browser_screenshot()
            if screenshot_data:
                emit('browser_screenshot', screenshot_data)

@socketio.on('test_credentials')
def handle_test_credentials(data):
    """Test YouTube/Google credentials"""
    global ultra_deleter
    if not ultra_deleter:
        emit('credentials_test_result', {'success': False, 'error': 'Browser not started'})
        return

    email = data.get('email')
    password = data.get('password')

    if not email or not password:
        emit('credentials_test_result', {'success': False, 'error': 'Email and password required'})
        return

    try:
        success = ultra_deleter.test_google_credentials(email, password)
        if success:
            emit('credentials_test_result', {'success': True})
        else:
            emit('credentials_test_result', {'success': False, 'error': 'Invalid credentials or login failed'})
    except Exception as e:
        emit('credentials_test_result', {'success': False, 'error': str(e)})

@socketio.on('auto_signin_with_credentials')
def handle_auto_signin_with_credentials(data):
    """Automatically sign in using provided credentials"""
    global ultra_deleter
    if not ultra_deleter:
        emit('signin_result', {'success': False, 'error': 'Browser not started'})
        return

    email = data.get('email')
    password = data.get('password')

    if not email or not password:
        emit('signin_result', {'success': False, 'error': 'Email and password required'})
        return

    try:
        success = ultra_deleter.auto_signin_with_credentials(email, password)

        if success:
            # Send immediate success signal
            emit('signin_result', {'success': True})

            # Wait for page to fully load and check for comments section
            import time
            time.sleep(3)  # Wait for page to load

            # Force screenshot update
            screenshot_data = ultra_deleter.get_browser_screenshot()
            if screenshot_data:
                emit('browser_screenshot', screenshot_data)

            # Check multiple times for the comments section (page might still be loading)
            comments_found = False
            for attempt in range(3):  # Try 3 times with delays
                # Use silent=True for automatic attempts to avoid spam
                silent_check = attempt < 2  # Only show messages on the last attempt
                if ultra_deleter.check_youtube_comments_page(silent=silent_check):
                    emit('auto_comments_ready', {'success': True})
                    print("🎯 Auto-detected 'Your YouTube Comments' section after sign-in")
                    comments_found = True
                    break
                else:
                    print(f"⏳ Attempt {attempt + 1}: Comments section not found yet, waiting...")
                    time.sleep(2)  # Wait 2 seconds between attempts

            if not comments_found:
                emit('auto_comments_ready', {'success': False})
                print("⚠️ 'Your YouTube Comments' section not found after multiple attempts")
        else:
            emit('signin_result', {'success': False, 'error': 'Sign-in failed'})

    except Exception as e:
        emit('signin_result', {'success': False, 'error': str(e)})

def send_progress_update(message):
    """Send progress update to all connected clients"""
    # Handle both string messages and structured messages
    if isinstance(message, dict):
        print(f"🔧 DEBUG: Structured message received: {message}")  # Debug logging
        # Structured message with type and status
        if message.get('type') == 'completion':
            print(f"🎉 DEBUG: Sending completion signal to frontend")  # Debug logging
            socketio.emit('deletion_complete', {
                'message': message['message'],
                'total_deleted': message['total_deleted'],
                'status': 'complete'
            })
        elif message.get('type') == 'error':
            print(f"❌ DEBUG: Sending error signal to frontend")  # Debug logging
            socketio.emit('deletion_error', {
                'message': message['message'],
                'status': 'error'
            })
        else:
            socketio.emit('progress_update', message)
    else:
        # Simple string message - also check for completion keywords
        print(f"📝 DEBUG: String message: {message}")  # Debug logging

        # Check if this string message indicates completion
        if any(keyword in message.lower() for keyword in ['complete', 'finished', 'done', 'no more']):
            print(f"🎉 DEBUG: Detected completion in string message, sending completion signal")
            socketio.emit('deletion_complete', {
                'message': message,
                'total_deleted': 0,  # Will be updated if available
                'status': 'complete'
            })

        socketio.emit('progress_update', {'message': message})

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy', 'version': 'ultra_fast', 'speed': '4_deletes_per_second'})

if __name__ == '__main__':
    print("🚀 Starting Ultra-Fast YouTube Comment Deleter...")
    print("⚡ Target Speed: 4 deletes per second")
    print("🌐 Open your browser and go to: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
