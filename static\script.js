// Initialize Socket.IO connection
const socket = io();

// DOM elements
const startBrowserBtn = document.getElementById('startBrowserBtn');
const startDeletionBtn = document.getElementById('startDeletionBtn');
const stopDeletionBtn = document.getElementById('stopDeletionBtn');
const closeBrowserBtn = document.getElementById('closeBrowserBtn');
const clearLogBtn = document.getElementById('clearLogBtn');
const logOutput = document.getElementById('logOutput');
const progressText = document.getElementById('progressText');
const progressFill = document.getElementById('progressFill');
const loginStatus = document.getElementById('loginStatus');
const confirmModal = document.getElementById('confirmModal');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');

// State variables
let isDeleting = false;
let browserStarted = false;

// Event listeners
startBrowserBtn.addEventListener('click', startBrowser);
startDeletionBtn.addEventListener('click', showConfirmModal);
stopDeletionBtn.addEventListener('click', stopDeletion);
closeBrowserBtn.addEventListener('click', closeBrowser);
clearLogBtn.addEventListener('click', clearLog);
confirmDeleteBtn.addEventListener('click', confirmDeletion);
cancelDeleteBtn.addEventListener('click', hideConfirmModal);

// Socket event listeners
socket.on('connect', function() {
    addLog('Connected to server');
});

socket.on('disconnect', function() {
    addLog('Disconnected from server');
});

socket.on('browser_ready', function(data) {
    if (data.success) {
        browserStarted = true;
        startBrowserBtn.textContent = 'Browser Started ✓';
        startBrowserBtn.disabled = true;
        showStep(2);
        showStep(3);
        addLog('Browser started successfully. Please log in to YouTube.');
        loginStatus.textContent = 'Please log in to YouTube in the browser window';
        loginStatus.style.color = '#ffc107';
    } else {
        addLog(`Error starting browser: ${data.error}`);
        startBrowserBtn.disabled = false;
    }
});

socket.on('deletion_status', function(data) {
    if (data.started) {
        isDeleting = true;
        startDeletionBtn.style.display = 'none';
        stopDeletionBtn.style.display = 'inline-block';
        showStep(4);
        addLog('Deletion process started...');
        progressText.textContent = 'Deletion in progress...';
    } else if (data.stopped) {
        isDeleting = false;
        startDeletionBtn.style.display = 'inline-block';
        stopDeletionBtn.style.display = 'none';
        addLog('Deletion process stopped by user');
        progressText.textContent = 'Deletion stopped';
    } else if (data.error) {
        addLog(`Error: ${data.error}`);
    }
});

socket.on('progress_update', function(data) {
    addLog(data.message);
    
    // Update login status if login is detected
    if (data.message.includes('Login detected') || data.message.includes('Ready to start')) {
        loginStatus.textContent = 'Login successful ✓';
        loginStatus.style.color = '#28a745';
    }
    
    // Update progress text
    progressText.textContent = data.message;
    
    // Extract number of deleted comments for progress bar
    const match = data.message.match(/Deleted comment #(\d+)/);
    if (match) {
        const count = parseInt(match[1]);
        // Simulate progress (since we don't know total count)
        const progress = Math.min((count * 2), 100);
        progressFill.style.width = `${progress}%`;
    }
    
    // Reset progress bar when completed
    if (data.message.includes('completed')) {
        progressFill.style.width = '100%';
        isDeleting = false;
        startDeletionBtn.style.display = 'inline-block';
        stopDeletionBtn.style.display = 'none';
    }
});

socket.on('browser_status', function(data) {
    if (data.closed) {
        browserStarted = false;
        startBrowserBtn.textContent = 'Start Browser';
        startBrowserBtn.disabled = false;
        hideStep(2);
        hideStep(3);
        hideStep(4);
        addLog('Browser closed');
        resetUI();
    }
});

// Functions
function startBrowser() {
    startBrowserBtn.disabled = true;
    startBrowserBtn.textContent = 'Starting Browser...';
    addLog('Starting browser...');
    socket.emit('start_browser');
}

function showConfirmModal() {
    confirmModal.style.display = 'flex';
}

function hideConfirmModal() {
    confirmModal.style.display = 'none';
}

function confirmDeletion() {
    hideConfirmModal();
    socket.emit('start_deletion');
}

function stopDeletion() {
    socket.emit('stop_deletion');
}

function closeBrowser() {
    socket.emit('close_browser');
}

function clearLog() {
    logOutput.innerHTML = '<p>Log cleared.</p>';
}

function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('p');
    logEntry.textContent = `[${timestamp}] ${message}`;
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

function showStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.style.display = 'block';
    }
}

function hideStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.style.display = 'none';
    }
}

function resetUI() {
    progressFill.style.width = '0%';
    progressText.textContent = 'Ready to start...';
    loginStatus.textContent = 'Waiting for login...';
    loginStatus.style.color = '#666';
    isDeleting = false;
    startDeletionBtn.style.display = 'inline-block';
    stopDeletionBtn.style.display = 'none';
}

// Initialize UI
document.addEventListener('DOMContentLoaded', function() {
    addLog('YouTube Comment Deleter loaded. Ready to start.');
});
