<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra-Fast YouTube Comment Deleter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .speed-indicator {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            font-weight: bold;
        }

        .content {
            padding: 30px;
        }

        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #ff6b6b;
        }

        .step.active {
            border-left-color: #28a745;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-ultra {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 18px;
            padding: 15px 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            font-size: 14px;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: #856404;
        }

        .ultra-warning {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(255, 107, 107, 0.5); }
            to { box-shadow: 0 0 30px rgba(255, 107, 107, 0.8); }
        }

        .speed-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #ff6b6b;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }

        .config-controls {
            margin-top: 15px;
        }

        .config-item {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .config-item label {
            min-width: 200px;
            font-weight: 600;
            color: #333;
        }

        .config-item input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
        }

        .config-item input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffc107;
            cursor: pointer;
        }

        .config-item span {
            min-width: 40px;
            font-weight: bold;
            color: #ffc107;
        }

        .config-presets {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-preset {
            background: #6c757d;
            color: white;
            font-size: 14px;
            padding: 8px 16px;
        }

        .btn-preset:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Ultra-Fast YouTube Comment Deleter</h1>
            <p>Maximum Speed Comment Deletion</p>
            <div class="speed-indicator">⚡ 4 DELETES PER SECOND ⚡</div>
        </header>

        <div class="content">
            <div class="warning ultra-warning">
                <h3>⚠️ ULTRA-FAST MODE WARNING ⚠️</h3>
                <p><strong>This will delete comments at 4 per second!</strong></p>
                <p>This is EXTREMELY FAST and cannot be undone!</p>
                <p>Use only if you're absolutely certain!</p>
            </div>

            <div class="speed-stats">
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">Deletes/Second</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">240</div>
                    <div class="stat-label">Deletes/Minute</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="refreshIntervalDisplay">10s</div>
                    <div class="stat-label">Auto-Refresh</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="stuckTimeDisplay">8s</div>
                    <div class="stat-label">Stuck Detection</div>
                </div>
            </div>

            <div class="config-section">
                <h3>⚙️ Refresh Configuration</h3>
                <p>Customize auto-refresh settings for optimal performance</p>

                <div class="config-controls">
                    <div class="config-item">
                        <label for="refreshInterval">Auto-Refresh Interval (seconds):</label>
                        <input type="range" id="refreshInterval" min="5" max="120" value="10" step="5">
                        <span id="refreshValue">10s</span>
                    </div>

                    <div class="config-item">
                        <label for="stuckTime">Stuck Detection Time (seconds):</label>
                        <input type="range" id="stuckTime" min="3" max="60" value="8" step="1">
                        <span id="stuckValue">8s</span>
                    </div>

                    <div class="config-presets">
                        <button class="btn btn-preset" onclick="setPreset('aggressive')">⚡ Aggressive (5s/3s)</button>
                        <button class="btn btn-preset" onclick="setPreset('balanced')">⚖️ Balanced (15s/8s)</button>
                        <button class="btn btn-preset" onclick="setPreset('conservative')">🛡️ Conservative (30s/15s)</button>
                    </div>

                    <button id="updateSettingsBtn" class="btn btn-warning">🔄 Update Settings</button>
                </div>
            </div>

            <div class="step active" id="step1">
                <h3>Step 1: Start Ultra-Fast Browser</h3>
                <p>Launch optimized browser for maximum speed</p>
                <button id="startBtn" class="btn btn-primary">🚀 Start Ultra-Fast Browser</button>
            </div>

            <div class="step" id="step2" style="display: none;">
                <h3>Step 2: Quick Login Check</h3>
                <p>Sign in to YouTube and verify login status</p>
                <button id="checkLoginBtn" class="btn btn-primary">⚡ Quick Login Check</button>
            </div>

            <div class="step" id="step3" style="display: none;">
                <h3>Step 3: ULTRA-FAST DELETION</h3>
                <p>⚠️ WARNING: This will delete at 4 comments per second!</p>
                <button id="ultraDeleteBtn" class="btn btn-ultra">🔥 START ULTRA-FAST DELETION</button>
            </div>

            <div class="log-output" id="logOutput">
                <p>🚀 Ultra-Fast Comment Deleter ready!</p>
                <p>⚡ Target speed: 4 deletes per second</p>
                <p>Click "Start Ultra-Fast Browser" to begin.</p>
            </div>

            <button id="closeBtn" class="btn btn-secondary">Close Browser</button>
        </div>
    </div>

    <script>
        const socket = io();

        const startBtn = document.getElementById('startBtn');
        const checkLoginBtn = document.getElementById('checkLoginBtn');
        const ultraDeleteBtn = document.getElementById('ultraDeleteBtn');
        const closeBtn = document.getElementById('closeBtn');
        const logOutput = document.getElementById('logOutput');

        // Configuration controls
        const refreshInterval = document.getElementById('refreshInterval');
        const stuckTime = document.getElementById('stuckTime');
        const refreshValue = document.getElementById('refreshValue');
        const stuckValue = document.getElementById('stuckValue');
        const updateSettingsBtn = document.getElementById('updateSettingsBtn');
        const refreshIntervalDisplay = document.getElementById('refreshIntervalDisplay');
        const stuckTimeDisplay = document.getElementById('stuckTimeDisplay');

        // Event listeners
        startBtn.addEventListener('click', () => {
            startBtn.disabled = true;
            startBtn.textContent = 'Starting Ultra-Fast Browser...';

            // Send current settings with start browser request
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('start_browser', settings);
        });

        // Configuration event listeners
        refreshInterval.addEventListener('input', () => {
            refreshValue.textContent = refreshInterval.value + 's';
        });

        stuckTime.addEventListener('input', () => {
            stuckValue.textContent = stuckTime.value + 's';
        });

        updateSettingsBtn.addEventListener('click', () => {
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('update_refresh_settings', settings);
            updateSettingsBtn.textContent = 'Updating...';
            updateSettingsBtn.disabled = true;
        });

        checkLoginBtn.addEventListener('click', () => {
            checkLoginBtn.disabled = true;
            checkLoginBtn.textContent = 'Checking Login...';
            socket.emit('check_login');
        });

        ultraDeleteBtn.addEventListener('click', () => {
            if (confirm('⚠️ FINAL WARNING ⚠️\n\nThis will delete comments at 4 PER SECOND!\n\nThis is EXTREMELY FAST and CANNOT BE UNDONE!\n\nAre you absolutely sure?')) {
                if (confirm('Last chance! This will start IMMEDIATE ultra-fast deletion!\n\nClick OK to proceed with 4 deletes per second.')) {
                    ultraDeleteBtn.disabled = true;
                    ultraDeleteBtn.textContent = '🔥 ULTRA-FAST DELETION IN PROGRESS...';
                    socket.emit('start_ultra_fast_deletion');
                }
            }
        });

        closeBtn.addEventListener('click', () => {
            socket.emit('close_browser');
        });

        // Socket events
        socket.on('connect', () => {
            addLog('🔗 Connected to ultra-fast server');
        });

        socket.on('browser_ready', (data) => {
            if (data.success) {
                startBtn.textContent = 'Ultra-Fast Browser Started ✓';
                showStep(2);
                addLog('🚀 Ultra-fast browser ready! Please sign in to YouTube.');

                // Update display with actual settings
                if (data.refresh_interval) {
                    refreshIntervalDisplay.textContent = data.refresh_interval + 's';
                    addLog(`🔄 Auto-refresh: ${data.refresh_interval}s intervals`);
                }
                if (data.stuck_time) {
                    stuckTimeDisplay.textContent = data.stuck_time + 's';
                    addLog(`⏱️ Stuck detection: ${data.stuck_time}s timeout`);
                }
            } else {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('settings_updated', (data) => {
            refreshIntervalDisplay.textContent = data.refresh_interval + 's';
            stuckTimeDisplay.textContent = data.stuck_time + 's';
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`✅ Settings updated: ${data.refresh_interval}s refresh, ${data.stuck_time}s stuck detection`);
        });

        socket.on('settings_error', (data) => {
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`❌ Settings error: ${data.error}`);
        });

        socket.on('login_ready', (data) => {
            if (data.success) {
                checkLoginBtn.textContent = 'Login Verified ✓';
                showStep(3);
                addLog('✅ Login verified! Ready for ULTRA-FAST deletion.');
            } else {
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                addLog('❌ Login not detected. Please sign in first.');
            }
        });

        socket.on('deletion_status', (data) => {
            if (data.started) {
                addLog('🔥 ULTRA-FAST DELETION STARTED! (4 deletes/second)');
            } else if (data.error) {
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('progress_update', (data) => {
            addLog(data.message);
            if (data.message.includes('complete')) {
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = 'Ultra-Fast Deletion Complete ✓';
            }
        });

        socket.on('browser_status', (data) => {
            if (data.closed) {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                hideStep(2);
                hideStep(3);
                addLog('🔴 Browser closed');
            }
        });

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const p = document.createElement('p');
            p.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(p);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function showStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'block';
            document.getElementById(`step${stepNum}`).classList.add('active');
        }

        function hideStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'none';
            document.getElementById(`step${stepNum}`).classList.remove('active');
        }

        // Preset configuration functions
        function setPreset(preset) {
            let refreshVal, stuckVal;

            switch(preset) {
                case 'aggressive':
                    refreshVal = 5;
                    stuckVal = 3;
                    break;
                case 'balanced':
                    refreshVal = 15;
                    stuckVal = 8;
                    break;
                case 'conservative':
                    refreshVal = 30;
                    stuckVal = 15;
                    break;
                default:
                    return;
            }

            refreshInterval.value = refreshVal;
            stuckTime.value = stuckVal;
            refreshValue.textContent = refreshVal + 's';
            stuckValue.textContent = stuckVal + 's';

            addLog(`⚙️ Applied ${preset} preset: ${refreshVal}s refresh, ${stuckVal}s stuck detection`);
        }
    </script>
</body>
</html>
