<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra-Fast YouTube Comment Deleter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: #334155;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 100;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
        }

        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #1e293b;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        .nav-item.active {
            background: #eff6ff;
            color: #3b82f6;
            border-right: 2px solid #3b82f6;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 24px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left h1 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            color: #1e293b;
        }

        .header-left p {
            font-size: 14px;
            color: #64748b;
            margin: 4px 0 0 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: #dcfce7;
            color: #166534;
        }

        .status-disconnected {
            background: #fef2f2;
            color: #991b1b;
        }

        .account-section {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .account-section:hover {
            background: #f1f5f9;
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .account-info {
            display: flex;
            flex-direction: column;
        }

        .account-name {
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            margin: 0;
        }

        .account-email {
            font-size: 12px;
            color: #64748b;
            margin: 0;
        }

        .speed-indicator {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 24px 32px;
            overflow-y: auto;
        }

        .page-section {
            display: none;
        }

        .page-section.active {
            display: block;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }

        .page-description {
            font-size: 14px;
            color: #64748b;
            margin: 0;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 24px;
        }

        .left-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
            height: fit-content;
        }

        .right-panel {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
            height: fit-content;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .settings-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .settings-card h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .settings-card p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 16px 0;
        }

        .step {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e2e8f0;
        }

        .step.active {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .step h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .step p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 12px 0;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 4px 0;
            display: inline-block;
        }

        .btn-full {
            width: 100%;
        }

        .btn-compact {
            width: auto;
            min-width: 140px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-ultra {
            background: #ef4444;
            color: white;
            font-size: 16px;
            padding: 12px 24px;
            font-weight: 600;
        }

        .btn-ultra:hover {
            background: #dc2626;
        }

        .log-output {
            background: #0f172a;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            height: 600px;
            overflow-y: auto;
            font-size: 13px;
            border: 1px solid #1e293b;
        }

        .log-output::-webkit-scrollbar {
            width: 6px;
        }

        .log-output::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .log-output::-webkit-scrollbar-thumb {
            background: #475569;
            border-radius: 3px;
        }

        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }

        .speed-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin: 24px 0;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 12px;
            font-weight: 500;
        }

        .config-section {
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 16px;
            margin-bottom: 16px;
        }

        .config-section h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .config-section p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 16px 0;
        }

        .config-controls {
            margin-top: 15px;
        }

        .config-item {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .config-item label {
            min-width: 200px;
            font-weight: 600;
            color: #333;
        }

        .config-item input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
        }

        .config-item input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffc107;
            cursor: pointer;
        }

        .config-item span {
            min-width: 40px;
            font-weight: bold;
            color: #ffc107;
        }

        .config-presets {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-preset {
            background: #6c757d;
            color: white;
            font-size: 14px;
            padding: 8px 16px;
        }

        .btn-preset:hover {
            background: #5a6268;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }

            .main-content {
                margin-left: 200px;
            }

            .content {
                padding: 16px 20px;
            }

            header {
                padding: 16px 20px;
            }

            .header-left h1 {
                font-size: 20px;
            }

            .header-left p {
                font-size: 12px;
            }

            .account-info {
                display: none;
            }

            .speed-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .left-panel, .right-panel {
                padding: 16px;
            }

            .log-output {
                height: 300px;
            }

            .config-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .speed-stats {
                grid-template-columns: 1fr;
            }

            .config-presets {
                flex-direction: column;
            }

            .btn-preset {
                width: 100%;
                margin: 4px 0;
            }

            .nav-item {
                padding: 16px 20px;
            }

            .sidebar-header {
                padding: 16px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>YT Manager</h2>
            </div>
            <nav class="sidebar-nav">
                <button class="nav-item active" onclick="showPage('home')">
                    <div class="nav-item-icon">🏠</div>
                    Home
                </button>
                <button class="nav-item" onclick="showPage('deletion')">
                    <div class="nav-item-icon">🗑️</div>
                    Comment Deletion
                </button>
                <button class="nav-item" onclick="showPage('settings')">
                    <div class="nav-item-icon">⚙️</div>
                    Settings
                </button>
                <button class="nav-item" onclick="showPage('configuration')">
                    <div class="nav-item-icon">🔧</div>
                    Configuration
                </button>
                <button class="nav-item" onclick="showPage('analytics')">
                    <div class="nav-item-icon">📊</div>
                    Analytics
                </button>
                <button class="nav-item" onclick="showPage('help')">
                    <div class="nav-item-icon">❓</div>
                    Help & Support
                </button>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <header>
                <div class="header-left">
                    <h1>YouTube Comment Manager</h1>
                    <p>Ultra-fast comment deletion at 4 deletes/second</p>
                </div>
                <div class="header-right">
                    <div class="status-badge status-connected">Connected</div>
                    <div class="account-section">
                        <div class="profile-pic">JD</div>
                        <div class="account-info">
                            <div class="account-name">John Doe</div>
                            <div class="account-email"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content">
                <!-- Home Page -->
                <div class="page-section active" id="page-home">
                    <div class="page-header">
                        <h1 class="page-title">Dashboard</h1>
                        <p class="page-description">Overview of your YouTube comment management</p>
                    </div>

                    <div class="warning">
                        ⚠️ This tool deletes comments at 4 per second. Use with caution as deletions cannot be undone.
                    </div>

                    <div class="speed-stats">
                        <div class="stat-card">
                            <div class="stat-number">4</div>
                            <div class="stat-label">Deletes/Second</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">240</div>
                            <div class="stat-label">Deletes/Minute</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="refreshIntervalDisplay">10s</div>
                            <div class="stat-label">Auto-Refresh</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stuckTimeDisplay">8s</div>
                            <div class="stat-label">Stuck Detection</div>
                        </div>
                    </div>

                    <div class="main-grid">
                        <div class="left-panel">
                            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #1e293b;">Quick Actions</h3>

                            <div class="step active" id="step1">
                                <h3>Step 1: Start Ultra-Fast Browser</h3>
                                <p>Launch optimized browser for maximum speed</p>
                                <button id="startBtn" class="btn btn-primary btn-compact">🚀 Start Browser</button>
                            </div>

                            <div class="step" id="step2" style="display: none;">
                                <h3>Step 2: Quick Login Check</h3>
                                <p>Sign in to YouTube and verify login status</p>
                                <button id="checkLoginBtn" class="btn btn-primary btn-compact">⚡ Login Check</button>
                            </div>

                            <div class="step" id="step3" style="display: none;">
                                <h3>Step 3: ULTRA-FAST DELETION</h3>
                                <p>⚠️ WARNING: This will delete at 4 comments per second!</p>
                                <button id="ultraDeleteBtn" class="btn btn-ultra btn-compact">🔥 START DELETION</button>

                                <div id="completionStatus" style="display: none; margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                                    <h4>🎉 Deletion Complete!</h4>
                                    <p id="completionMessage">All comments have been successfully deleted.</p>
                                    <div id="completionStats" style="margin-top: 10px; font-weight: bold;"></div>
                                </div>
                            </div>

                            <button id="closeBtn" class="btn btn-secondary btn-full" style="margin-top: 20px;">Close Browser</button>
                        </div>

                        <div class="right-panel">
                            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #1e293b;">Console Output</h3>
                            <div class="log-output" id="logOutput">
                                <p>System ready. Click "Start Ultra-Fast Browser" to begin.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deletion Page -->
                <div class="page-section" id="page-deletion">
                    <div class="page-header">
                        <h1 class="page-title">Comment Deletion</h1>
                        <p class="page-description">Manage and delete your YouTube comments</p>
                    </div>
                    <div class="settings-card">
                        <h3>Deletion Tools</h3>
                        <p>Advanced deletion options and bulk operations</p>
                        <button class="btn btn-primary">Coming Soon</button>
                    </div>
                </div>

                <!-- Settings Page -->
                <div class="page-section" id="page-settings">
                    <div class="page-header">
                        <h1 class="page-title">Settings</h1>
                        <p class="page-description">Configure your account and application preferences</p>
                    </div>
                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Account Settings</h3>
                            <p>Manage your profile and account information</p>
                            <button class="btn btn-primary">Edit Profile</button>
                        </div>
                        <div class="settings-card">
                            <h3>Notifications</h3>
                            <p>Configure notification preferences</p>
                            <button class="btn btn-primary">Configure</button>
                        </div>
                    </div>
                </div>

                <!-- Configuration Page -->
                <div class="page-section" id="page-configuration">
                    <div class="page-header">
                        <h1 class="page-title">Configuration</h1>
                        <p class="page-description">Advanced settings and performance tuning</p>
                    </div>

                    <div class="settings-card">
                        <h3>⚙️ Refresh Configuration</h3>
                        <p>Customize auto-refresh settings for optimal performance</p>

                        <div class="config-controls">
                            <div class="config-item">
                                <label for="refreshInterval">Auto-Refresh Interval (seconds):</label>
                                <input type="range" id="refreshInterval" min="5" max="120" value="10" step="5">
                                <span id="refreshValue">10s</span>
                            </div>

                            <div class="config-item">
                                <label for="stuckTime">Stuck Detection Time (seconds):</label>
                                <input type="range" id="stuckTime" min="3" max="60" value="8" step="1">
                                <span id="stuckValue">8s</span>
                            </div>

                            <div class="config-presets">
                                <button class="btn btn-preset btn-compact" onclick="setPreset('aggressive')">⚡ Aggressive</button>
                                <button class="btn btn-preset btn-compact" onclick="setPreset('balanced')">⚖️ Balanced</button>
                                <button class="btn btn-preset btn-compact" onclick="setPreset('conservative')">🛡️ Conservative</button>
                            </div>

                            <button id="updateSettingsBtn" class="btn btn-warning btn-compact">🔄 Update Settings</button>
                        </div>
                    </div>
                </div>

                <!-- Analytics Page -->
                <div class="page-section" id="page-analytics">
                    <div class="page-header">
                        <h1 class="page-title">Analytics</h1>
                        <p class="page-description">View deletion statistics and performance metrics</p>
                    </div>
                    <div class="settings-card">
                        <h3>Deletion Statistics</h3>
                        <p>Track your comment deletion history and performance</p>
                        <button class="btn btn-primary">View Reports</button>
                    </div>
                </div>

                <!-- Help Page -->
                <div class="page-section" id="page-help">
                    <div class="page-header">
                        <h1 class="page-title">Help & Support</h1>
                        <p class="page-description">Get help and support for using the application</p>
                    </div>
                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Documentation</h3>
                            <p>Learn how to use all features effectively</p>
                            <button class="btn btn-primary">View Docs</button>
                        </div>
                        <div class="settings-card">
                            <h3>Contact Support</h3>
                            <p>Get help from our support team</p>
                            <button class="btn btn-primary">Contact Us</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();

        const startBtn = document.getElementById('startBtn');
        const checkLoginBtn = document.getElementById('checkLoginBtn');
        const ultraDeleteBtn = document.getElementById('ultraDeleteBtn');
        const closeBtn = document.getElementById('closeBtn');
        const logOutput = document.getElementById('logOutput');

        // Configuration controls
        const refreshInterval = document.getElementById('refreshInterval');
        const stuckTime = document.getElementById('stuckTime');
        const refreshValue = document.getElementById('refreshValue');
        const stuckValue = document.getElementById('stuckValue');
        const updateSettingsBtn = document.getElementById('updateSettingsBtn');
        const refreshIntervalDisplay = document.getElementById('refreshIntervalDisplay');
        const stuckTimeDisplay = document.getElementById('stuckTimeDisplay');

        // Event listeners
        startBtn.addEventListener('click', () => {
            startBtn.disabled = true;
            startBtn.textContent = 'Starting Ultra-Fast Browser...';

            // Send current settings with start browser request
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('start_browser', settings);
        });

        // Configuration event listeners
        refreshInterval.addEventListener('input', () => {
            refreshValue.textContent = refreshInterval.value + 's';
        });

        stuckTime.addEventListener('input', () => {
            stuckValue.textContent = stuckTime.value + 's';
        });

        updateSettingsBtn.addEventListener('click', () => {
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('update_refresh_settings', settings);
            updateSettingsBtn.textContent = 'Updating...';
            updateSettingsBtn.disabled = true;
        });

        checkLoginBtn.addEventListener('click', () => {
            checkLoginBtn.disabled = true;
            checkLoginBtn.textContent = 'Checking Login...';
            socket.emit('check_login');
        });

        ultraDeleteBtn.addEventListener('click', () => {
            if (confirm('⚠️ FINAL WARNING ⚠️\n\nThis will delete comments at 4 PER SECOND!\n\nThis is EXTREMELY FAST and CANNOT BE UNDONE!\n\nAre you absolutely sure?')) {
                if (confirm('Last chance! This will start IMMEDIATE ultra-fast deletion!\n\nClick OK to proceed with 4 deletes per second.')) {
                    ultraDeleteBtn.disabled = true;
                    ultraDeleteBtn.textContent = '🔥 ULTRA-FAST DELETION IN PROGRESS...';
                    socket.emit('start_ultra_fast_deletion');
                }
            }
        });

        closeBtn.addEventListener('click', () => {
            socket.emit('close_browser');
        });

        // Socket events
        socket.on('connect', () => {
            addLog('🔗 Connected to ultra-fast server');
        });

        socket.on('browser_ready', (data) => {
            if (data.success) {
                startBtn.textContent = 'Ultra-Fast Browser Started ✓';
                showStep(2);
                addLog('🚀 Ultra-fast browser ready! Please sign in to YouTube.');

                // Update display with actual settings
                if (data.refresh_interval) {
                    refreshIntervalDisplay.textContent = data.refresh_interval + 's';
                    addLog(`🔄 Auto-refresh: ${data.refresh_interval}s intervals`);
                }
                if (data.stuck_time) {
                    stuckTimeDisplay.textContent = data.stuck_time + 's';
                    addLog(`⏱️ Stuck detection: ${data.stuck_time}s timeout`);
                }
            } else {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('settings_updated', (data) => {
            refreshIntervalDisplay.textContent = data.refresh_interval + 's';
            stuckTimeDisplay.textContent = data.stuck_time + 's';
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`✅ Settings updated: ${data.refresh_interval}s refresh, ${data.stuck_time}s stuck detection`);
        });

        socket.on('settings_error', (data) => {
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`❌ Settings error: ${data.error}`);
        });

        socket.on('login_ready', (data) => {
            if (data.success) {
                checkLoginBtn.textContent = 'Login Verified ✓';
                showStep(3);
                addLog('✅ Login verified! Ready for ULTRA-FAST deletion.');
            } else {
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                addLog('❌ Login not detected. Please sign in first.');
            }
        });

        socket.on('deletion_status', (data) => {
            if (data.started) {
                addLog('🔥 ULTRA-FAST DELETION STARTED! (4 deletes/second)');
            } else if (data.error) {
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('progress_update', (data) => {
            console.log('📝 DEBUG: Progress update received:', data);
            addLog(data.message);
        });

        socket.on('deletion_complete', (data) => {
            console.log('🎉 DEBUG: Deletion complete event received:', data);
            addLog('🎉 COMPLETION DETECTED BY FRONTEND!');
            addLog(data.message);

            ultraDeleteBtn.disabled = false;
            ultraDeleteBtn.textContent = '✅ Deletion Complete!';
            ultraDeleteBtn.style.background = '#28a745';

            // Show completion stats
            addLog(`📊 Final Stats: ${data.total_deleted} comments deleted successfully`);
            addLog('🎉 All YouTube comments have been removed!');

            // Update the interface to show completion
            const step3 = document.getElementById('step3');
            if (step3) {
                step3.style.background = '#d4edda';
                step3.style.borderLeftColor = '#28a745';
            }

            // Show completion status box
            const completionStatus = document.getElementById('completionStatus');
            const completionStats = document.getElementById('completionStats');

            if (completionStatus && completionStats) {
                completionStatus.style.display = 'block';
                completionStats.textContent = `Total Comments Deleted: ${data.total_deleted}`;
            }

            // Update page title
            document.title = `✅ Complete - ${data.total_deleted} Comments Deleted`;
        });

        socket.on('deletion_error', (data) => {
            addLog(`❌ ${data.message}`);
            ultraDeleteBtn.disabled = false;
            ultraDeleteBtn.textContent = '❌ Error Occurred';
            ultraDeleteBtn.style.background = '#dc3545';
        });

        socket.on('browser_status', (data) => {
            if (data.closed) {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                hideStep(2);
                hideStep(3);
                addLog('🔴 Browser closed');
            }
        });

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const p = document.createElement('p');
            p.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(p);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function showStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'block';
            document.getElementById(`step${stepNum}`).classList.add('active');
        }

        function hideStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'none';
            document.getElementById(`step${stepNum}`).classList.remove('active');
        }

        // Navigation functions
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-section').forEach(page => {
                page.classList.remove('active');
            });

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected page
            document.getElementById(`page-${pageId}`).classList.add('active');

            // Add active class to clicked nav item
            event.target.classList.add('active');
        }

        // Preset configuration functions
        function setPreset(preset) {
            let refreshVal, stuckVal;

            switch(preset) {
                case 'aggressive':
                    refreshVal = 5;
                    stuckVal = 3;
                    break;
                case 'balanced':
                    refreshVal = 15;
                    stuckVal = 8;
                    break;
                case 'conservative':
                    refreshVal = 30;
                    stuckVal = 15;
                    break;
                default:
                    return;
            }

            refreshInterval.value = refreshVal;
            stuckTime.value = stuckVal;
            refreshValue.textContent = refreshVal + 's';
            stuckValue.textContent = stuckVal + 's';

            addLog(`⚙️ Applied ${preset} preset: ${refreshVal}s refresh, ${stuckVal}s stuck detection`);
        }
    </script>
</body>
</html>
