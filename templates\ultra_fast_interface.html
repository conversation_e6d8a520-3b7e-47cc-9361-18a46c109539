<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra-Fast YouTube Comment Deleter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: #334155;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 240px;
            background: #181818;
            border-right: 1px solid #272727;
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 100;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #272727;
        }

        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #ff0000;
        }

        .sidebar-nav {
            flex: 1;
            padding: 16px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #aaaaaa;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-item:hover {
            background: #272727;
            color: #ffffff;
        }

        .nav-item.active {
            background: #ff0000;
            color: #ffffff;
            border-right: 3px solid #cc0000;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        header {
            background: #0f0f0f;
            border-bottom: 1px solid #272727;
            padding: 24px 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .header-center {
            text-align: center;
        }

        .header-center h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            color: #ff0000;
            text-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
        }

        .header-center p {
            font-size: 14px;
            color: #aaaaaa;
            margin: 4px 0 0 0;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
            position: absolute;
            right: 32px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: #dcfce7;
            color: #166534;
        }

        .status-disconnected {
            background: #fef2f2;
            color: #991b1b;
        }

        .account-section {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .account-section:hover {
            background: #272727;
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff0000, #cc0000);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .account-info {
            display: flex;
            flex-direction: column;
        }

        .account-name {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            margin: 0;
        }

        .account-email {
            font-size: 12px;
            color: #aaaaaa;
            margin: 0;
        }

        .speed-indicator {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 24px 32px;
            overflow-y: auto;
            background: #0f0f0f;
        }

        .page-section {
            display: none;
        }

        .page-section.active {
            display: block;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            margin: 0 0 8px 0;
        }

        .page-description {
            font-size: 14px;
            color: #aaaaaa;
            margin: 0;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 400px 1200px;
            gap: 200px;
        }

        .left-panel {
            background: #181818;
            border-radius: 12px;
            border: 1px solid #272727;
            padding: 24px;
            height: fit-content;
        }

        .right-panel {
            background: #181818;
            border-radius: 12px;
            border: 1px solid #272727;
            padding: 24px;
            height: fit-content;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .settings-card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 24px;
        }

        .settings-card h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .settings-card p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 16px 0;
        }

        .step {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e2e8f0;
        }

        .step.active {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .step h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .step p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 12px 0;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 4px 0;
            display: inline-block;
        }

        .btn-full {
            width: 100%;
        }

        .btn-compact {
            width: auto;
            min-width: 140px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-ultra {
            background: #ef4444;
            color: white;
            font-size: 16px;
            padding: 12px 24px;
            font-weight: 600;
        }

        .btn-ultra:hover {
            background: #dc2626;
        }

        .log-output {
            background: #0f172a;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            height: 450px;
            overflow-y: auto;
            font-size: 13px;
            border: 1px solid #1e293b;
        }

        .log-output::-webkit-scrollbar {
            width: 6px;
        }

        .log-output::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .log-output::-webkit-scrollbar-thumb {
            background: #475569;
            border-radius: 3px;
        }

        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }

        .speed-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin: 24px 0;
        }

        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #64748b;
            font-size: 12px;
            font-weight: 500;
        }

        .config-section {
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 16px;
            margin-bottom: 16px;
        }

        .config-section h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .config-section p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 16px 0;
        }

        .config-controls {
            margin-top: 15px;
        }

        .config-item {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .config-item label {
            min-width: 200px;
            font-weight: 600;
            color: #333;
        }

        .config-item input[type="range"] {
            flex: 1;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
        }

        .config-item input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ffc107;
            cursor: pointer;
        }

        .config-item span {
            min-width: 40px;
            font-weight: bold;
            color: #ffc107;
        }

        .credential-input {
            flex: 1;
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            color: #333;
            outline: none;
            transition: border-color 0.3s ease;
            min-width: 200px;
        }

        .credential-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .credential-input:hover {
            border-color: #999;
        }

        .credential-input::placeholder {
            color: #999;
            font-style: italic;
        }

        .config-presets {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-preset {
            background: #6c757d;
            color: white;
            font-size: 14px;
            padding: 8px 16px;
        }

        .btn-preset:hover {
            background: #5a6268;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }

            .main-content {
                margin-left: 200px;
            }

            .content {
                padding: 16px 20px;
            }

            header {
                padding: 16px 20px;
            }

            .header-left h1 {
                font-size: 20px;
            }

            .header-left p {
                font-size: 12px;
            }

            .account-info {
                display: none;
            }

            .speed-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .left-panel, .right-panel {
                padding: 16px;
            }

            .log-output {
                height: 300px;
            }

            .config-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .speed-stats {
                grid-template-columns: 1fr;
            }

            .config-presets {
                flex-direction: column;
            }

            .btn-preset {
                width: 100%;
                margin: 4px 0;
            }

            .nav-item {
                padding: 16px 20px;
            }

            .sidebar-header {
                padding: 16px 20px;
            }
        }

        /* Click feedback animation */
        @keyframes clickPulse {
            0% {
                transform: scale(0.5);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>YT Manager</h2>
            </div>
            <nav class="sidebar-nav">
                <button class="nav-item active" onclick="showPage('home')">
                    <div class="nav-item-icon">🏠</div>
                    Home
                </button>
                <button class="nav-item" onclick="showPage('deletion')">
                    <div class="nav-item-icon">🗑️</div>
                    Comment Deletion
                </button>
                <button class="nav-item" onclick="showPage('settings')">
                    <div class="nav-item-icon">⚙️</div>
                    Settings
                </button>
                <button class="nav-item" onclick="showPage('configuration')">
                    <div class="nav-item-icon">🔧</div>
                    Configuration
                </button>
                <button class="nav-item" onclick="showPage('analytics')">
                    <div class="nav-item-icon">📊</div>
                    Analytics
                </button>
                <button class="nav-item" onclick="showPage('help')">
                    <div class="nav-item-icon">❓</div>
                    Help & Support
                </button>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <header>
                <div class="header-center">
                    <h1>YouTube Comment Manager</h1>
                    <p>Ultra-fast comment deletion at 4 deletes/second</p>
                </div>
                <div class="header-right">
                    <div class="status-badge status-connected">Connected</div>
                    <div class="account-section">
                        <div class="profile-pic">JD</div>
                        <div class="account-info">
                            <div class="account-name">John Doe</div>
                            <div class="account-email"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content">
                <!-- Home Page -->
                <div class="page-section active" id="page-home">
                    <div class="page-header">
                        <h1 class="page-title">Dashboard</h1>
                        <p class="page-description">Overview of your YouTube comment management</p>
                    </div>

                    <div class="warning">
                        ⚠️ This tool deletes comments at 4 per second. Use with caution as deletions cannot be undone.
                    </div>

                    <div class="speed-stats">
                        <div class="stat-card">
                            <div class="stat-number">4</div>
                            <div class="stat-label">Deletes/Second</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">240</div>
                            <div class="stat-label">Deletes/Minute</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="refreshIntervalDisplay">10s</div>
                            <div class="stat-label">Auto-Refresh</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="stuckTimeDisplay">8s</div>
                            <div class="stat-label">Stuck Detection</div>
                        </div>
                    </div>

                    <div class="main-grid">
                        <div class="left-panel">
                            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #ffffff;">Quick Actions</h3>

                            <div class="step active" id="step1">
                                <h3>Step 1: Start Ultra-Fast Browser</h3>
                                <p>Launch optimized browser for maximum speed</p>
                                <button id="startBtn" class="btn btn-primary btn-compact">🚀 Start Browser</button>
                            </div>

                            <div class="step" id="step2" style="display: none;">
                                <h3>Step 2: Sign In to YouTube</h3>
                                <p>Use the interactive browser or auto-find the sign-in button</p>
                                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                    <button id="findSigninBtn" class="btn btn-secondary btn-compact">🎯 Find Sign-In</button>
                                    <button id="autoSigninBtn" class="btn btn-secondary btn-compact">🔑 Auto Click</button>
                                    <button id="autoSignInWithCredsBtn" class="btn btn-primary btn-compact">🚀 Auto Sign-In</button>
                                    <button id="checkLoginBtn" class="btn btn-primary btn-compact">⚡ Login Check</button>
                                </div>
                            </div>

                            <div class="step" id="step3" style="display: none;">
                                <h3>Step 3: ULTRA-FAST DELETION</h3>
                                <p>⚠️ WARNING: This will delete at 4 comments per second!</p>
                                <button id="ultraDeleteBtn" class="btn btn-ultra btn-compact">🔥 START DELETION</button>

                                <div id="completionStatus" style="display: none; margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                                    <h4>🎉 Deletion Complete!</h4>
                                    <p id="completionMessage">All comments have been successfully deleted.</p>
                                    <div id="completionStats" style="margin-top: 10px; font-weight: bold;"></div>
                                </div>
                            </div>

                            <button id="closeBtn" class="btn btn-secondary btn-full" style="margin-top: 20px;">Close Browser</button>

                            <!-- Debug Test Button -->
                            <button id="testCompletionBtn" class="btn btn-warning btn-full" style="margin-top: 10px;">🧪 Test Completion UI</button>
                        </div>

                        <div class="right-panel">
                            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #ffffff;">Console Output</h3>

                            <!-- Browser Screenshot Area -->
                            <div id="browserView" style="display: none; margin-bottom: 16px;">
                                <h4 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: #ffffff;">🖥️ Interactive Browser</h4>
                                <div style="border: 1px solid #272727; border-radius: 8px; overflow: hidden; background: #000;">
                                    <img id="browserScreenshot" src="" alt="Browser Screenshot" style="width: 100%; height: auto; cursor: pointer; display: none;" />
                                    <div id="browserLoading" style="padding: 40px; text-align: center; color: #aaaaaa;">
                                        Loading browser view...
                                    </div>
                                </div>
                                <div style="margin-top: 8px; display: flex; justify-content: space-between; align-items: center;">
                                    <span style="font-size: 12px; color: #aaaaaa;">
                                        Click on the browser image to interact • Type to enter text
                                    </span>
                                    <button id="refreshScreenshotBtn" class="btn btn-secondary" style="padding: 4px 8px; font-size: 11px;">🔄 Refresh</button>
                                </div>
                            </div>

                            <div class="log-output" id="logOutput">
                                <p>System ready. Click "Start Ultra-Fast Browser" to begin.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deletion Page -->
                <div class="page-section" id="page-deletion">
                    <div class="page-header">
                        <h1 class="page-title">Comment Deletion</h1>
                        <p class="page-description">Manage and delete your YouTube comments</p>
                    </div>
                    <div class="settings-card">
                        <h3>Deletion Tools</h3>
                        <p>Advanced deletion options and bulk operations</p>
                        <button class="btn btn-primary">Coming Soon</button>
                    </div>
                </div>

                <!-- Settings Page -->
                <div class="page-section" id="page-settings">
                    <div class="page-header">
                        <h1 class="page-title">Settings</h1>
                        <p class="page-description">Configure your account and application preferences</p>
                    </div>

                    <!-- YouTube Credentials Section -->
                    <div class="config-section">
                        <h3>🔐 YouTube/Google Credentials</h3>
                        <p>Enter your YouTube/Google account credentials for automatic sign-in</p>

                        <div class="config-controls">
                            <div class="config-item">
                                <label for="ytEmail">Email Address:</label>
                                <input type="email" id="ytEmail" placeholder="<EMAIL>" class="credential-input">
                            </div>

                            <div class="config-item">
                                <label for="ytPassword">Password:</label>
                                <input type="password" id="ytPassword" placeholder="Your password" class="credential-input">
                            </div>

                            <div class="config-item">
                                <button id="saveCredentialsBtn" class="btn btn-primary btn-compact">💾 Save Credentials</button>
                                <button id="testCredentialsBtn" class="btn btn-secondary btn-compact">🧪 Test Login</button>
                                <button id="clearCredentialsBtn" class="btn btn-warning btn-compact">🗑️ Clear</button>
                                <button id="testInputBtn" class="btn btn-secondary btn-compact">🔍 Test Input</button>
                            </div>

                            <div id="credentialsStatus" style="margin-top: 10px; padding: 8px; border-radius: 4px; display: none;">
                                <span id="credentialsStatusText"></span>
                            </div>
                        </div>

                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 12px; border-radius: 6px; margin-top: 16px; font-size: 13px; color: #856404;">
                            <strong>🔒 Security Note:</strong> Credentials are stored locally in your browser and are never sent to external servers. They are only used for automatic sign-in to YouTube.
                        </div>
                    </div>

                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Auto Sign-In Settings</h3>
                            <p>Configure automatic sign-in behavior</p>
                            <div class="config-controls">
                                <div class="config-item">
                                    <label>
                                        <input type="checkbox" id="autoSignInEnabled" style="margin-right: 8px;">
                                        Enable automatic sign-in
                                    </label>
                                </div>
                                <div class="config-item">
                                    <label>
                                        <input type="checkbox" id="rememberCredentials" style="margin-right: 8px;" checked>
                                        Remember credentials
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="settings-card">
                            <h3>Notifications</h3>
                            <p>Configure notification preferences</p>
                            <button class="btn btn-primary">Configure</button>
                        </div>
                    </div>
                </div>

                <!-- Configuration Page -->
                <div class="page-section" id="page-configuration">
                    <div class="page-header">
                        <h1 class="page-title">Configuration</h1>
                        <p class="page-description">Advanced settings and performance tuning</p>
                    </div>

                    <div class="settings-card">
                        <h3>⚙️ Refresh Configuration</h3>
                        <p>Customize auto-refresh settings for optimal performance</p>

                        <div class="config-controls">
                            <div class="config-item">
                                <label for="refreshInterval">Auto-Refresh Interval (seconds):</label>
                                <input type="range" id="refreshInterval" min="5" max="120" value="10" step="5">
                                <span id="refreshValue">10s</span>
                            </div>

                            <div class="config-item">
                                <label for="stuckTime">Stuck Detection Time (seconds):</label>
                                <input type="range" id="stuckTime" min="3" max="60" value="8" step="1">
                                <span id="stuckValue">8s</span>
                            </div>

                            <div class="config-presets">
                                <button class="btn btn-preset btn-compact" onclick="setPreset('aggressive')">⚡ Aggressive</button>
                                <button class="btn btn-preset btn-compact" onclick="setPreset('balanced')">⚖️ Balanced</button>
                                <button class="btn btn-preset btn-compact" onclick="setPreset('conservative')">🛡️ Conservative</button>
                            </div>

                            <button id="updateSettingsBtn" class="btn btn-warning btn-compact">🔄 Update Settings</button>
                        </div>
                    </div>
                </div>

                <!-- Analytics Page -->
                <div class="page-section" id="page-analytics">
                    <div class="page-header">
                        <h1 class="page-title">Analytics</h1>
                        <p class="page-description">View deletion statistics and performance metrics</p>
                    </div>
                    <div class="settings-card">
                        <h3>Deletion Statistics</h3>
                        <p>Track your comment deletion history and performance</p>
                        <button class="btn btn-primary">View Reports</button>
                    </div>
                </div>

                <!-- Help Page -->
                <div class="page-section" id="page-help">
                    <div class="page-header">
                        <h1 class="page-title">Help & Support</h1>
                        <p class="page-description">Get help and support for using the application</p>
                    </div>
                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Documentation</h3>
                            <p>Learn how to use all features effectively</p>
                            <button class="btn btn-primary">View Docs</button>
                        </div>
                        <div class="settings-card">
                            <h3>Contact Support</h3>
                            <p>Get help from our support team</p>
                            <button class="btn btn-primary">Contact Us</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection with optimizations for local performance
        const socket = io({
            // Performance optimizations for local connection
            transports: ['websocket', 'polling'],  // Prefer websocket
            upgrade: true,
            rememberUpgrade: true,
            timeout: 3000,  // Reduce connection timeout for local
            forceNew: false,  // Reuse connections
            reconnection: true,
            reconnectionDelay: 500,  // Faster reconnection for local
            reconnectionAttempts: 10,
            maxReconnectionAttempts: 10
        });

        const startBtn = document.getElementById('startBtn');
        const findSigninBtn = document.getElementById('findSigninBtn');
        const autoSigninBtn = document.getElementById('autoSigninBtn');
        const autoSignInWithCredsBtn = document.getElementById('autoSignInWithCredsBtn');
        const checkLoginBtn = document.getElementById('checkLoginBtn');
        const ultraDeleteBtn = document.getElementById('ultraDeleteBtn');
        const closeBtn = document.getElementById('closeBtn');
        const refreshScreenshotBtn = document.getElementById('refreshScreenshotBtn');
        const testCompletionBtn = document.getElementById('testCompletionBtn');
        const logOutput = document.getElementById('logOutput');

        // Configuration controls
        const refreshInterval = document.getElementById('refreshInterval');
        const stuckTime = document.getElementById('stuckTime');
        const refreshValue = document.getElementById('refreshValue');
        const stuckValue = document.getElementById('stuckValue');
        const updateSettingsBtn = document.getElementById('updateSettingsBtn');
        const refreshIntervalDisplay = document.getElementById('refreshIntervalDisplay');
        const stuckTimeDisplay = document.getElementById('stuckTimeDisplay');

        // Event listeners
        startBtn.addEventListener('click', () => {
            startBtn.disabled = true;
            startBtn.textContent = 'Starting Ultra-Fast Browser...';

            // Send current settings with start browser request
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('start_browser', settings);
        });

        // Configuration event listeners
        refreshInterval.addEventListener('input', () => {
            refreshValue.textContent = refreshInterval.value + 's';
        });

        stuckTime.addEventListener('input', () => {
            stuckValue.textContent = stuckTime.value + 's';
        });

        updateSettingsBtn.addEventListener('click', () => {
            const settings = {
                refresh_interval: parseInt(refreshInterval.value),
                stuck_time: parseInt(stuckTime.value)
            };

            socket.emit('update_refresh_settings', settings);
            updateSettingsBtn.textContent = 'Updating...';
            updateSettingsBtn.disabled = true;
        });

        findSigninBtn.addEventListener('click', () => {
            findSigninBtn.disabled = true;
            findSigninBtn.textContent = 'Finding...';
            socket.emit('find_signin');
        });

        autoSigninBtn.addEventListener('click', () => {
            autoSigninBtn.disabled = true;
            autoSigninBtn.textContent = 'Clicking...';
            socket.emit('auto_signin');
        });

        autoSignInWithCredsBtn.addEventListener('click', () => {
            const credentials = getStoredCredentials();

            if (!credentials.email || !credentials.password) {
                alert('Please set your YouTube credentials in Settings first!');
                showPage('settings');
                return;
            }

            autoSignInWithCredsBtn.disabled = true;
            autoSignInWithCredsBtn.textContent = '🔑 Signing In...';

            socket.emit('auto_signin_with_credentials', {
                email: credentials.email,
                password: credentials.password
            });
        });

        checkLoginBtn.addEventListener('click', () => {
            checkLoginBtn.disabled = true;
            checkLoginBtn.textContent = 'Checking Login...';
            socket.emit('check_login');
        });

        // Polling mechanism for completion detection (fallback)
        let deletionPollingInterval = null;

        function startDeletionPolling() {
            // Poll every 500ms for faster local response
            deletionPollingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/check_deletion_status');
                    const status = await response.json();

                    if (status.is_complete) {
                        console.log('🎯 POLLING: Deletion completion detected!', status);
                        clearInterval(deletionPollingInterval);
                        deletionPollingInterval = null;

                        // Trigger completion UI
                        handleDeletionCompletion({
                            message: status.completion_message,
                            total_deleted: status.total_deleted,
                            status: 'complete',
                            success: true,
                            source: 'polling'
                        });
                    }
                } catch (error) {
                    console.error('🔴 POLLING: Error checking deletion status:', error);
                }
            }, 500);  // 500ms polling for fast local response
        }

        function stopDeletionPolling() {
            if (deletionPollingInterval) {
                clearInterval(deletionPollingInterval);
                deletionPollingInterval = null;
                console.log('🛑 POLLING: Stopped deletion status polling');
            }
        }

        function handleDeletionCompletion(data) {
            console.log('🎉 COMPLETION: Handling deletion completion:', data);
            addLog('🎉 COMPLETION DETECTED!');
            addLog(`🎉 Source: ${data.source || 'websocket'}`);
            addLog(data.message);

            ultraDeleteBtn.disabled = false;
            ultraDeleteBtn.textContent = '✅ Deletion Complete!';
            ultraDeleteBtn.style.background = '#28a745';

            if (data.total_deleted !== undefined) {
                addLog(`📊 Final Stats: ${data.total_deleted} comments deleted successfully`);
            }
            addLog('🎉 All YouTube comments have been removed!');

            // Stop polling if it was running
            stopDeletionPolling();

            // Reset button after 5 seconds
            setTimeout(() => {
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                ultraDeleteBtn.style.background = '';
            }, 5000);
        }

        ultraDeleteBtn.addEventListener('click', async () => {
            if (confirm('⚠️ FINAL WARNING ⚠️\n\nThis will delete comments at 4 PER SECOND!\n\nThis is EXTREMELY FAST and CANNOT BE UNDONE!\n\nAre you absolutely sure?')) {
                if (confirm('Last chance! This will start IMMEDIATE ultra-fast deletion!\n\nClick OK to proceed with 4 deletes per second.')) {

                    // Reset deletion status on server
                    try {
                        await fetch('/reset_deletion_status', { method: 'POST' });
                        console.log('🔄 Reset deletion status on server');
                    } catch (error) {
                        console.error('⚠️ Failed to reset deletion status:', error);
                    }

                    ultraDeleteBtn.disabled = true;
                    ultraDeleteBtn.textContent = '🔥 ULTRA-FAST DELETION IN PROGRESS...';
                    ultraDeleteBtn.style.background = '#dc3545';

                    addLog('🚀 Starting ultra-fast deletion...');
                    addLog('⚡ Deleting at 4 comments per second...');
                    addLog('🔄 Starting completion polling as fallback...');

                    // Start polling for completion (fallback mechanism)
                    startDeletionPolling();

                    socket.emit('start_ultra_fast_deletion');
                }
            }
        });

        closeBtn.addEventListener('click', () => {
            socket.emit('close_browser');
        });

        testCompletionBtn.addEventListener('click', () => {
            // Manually trigger completion UI to test frontend
            console.log('🧪 Testing completion UI manually');
            addLog('🧪 Testing completion UI manually...');

            // Simulate completion data
            const testData = {
                message: '🧪 Test completion! Deleted 42 comments total.',
                total_deleted: 42,
                status: 'complete',
                success: true
            };

            // Manually trigger the completion handler
            console.log('🎉 DEBUG: Manual completion event triggered:', testData);
            addLog('🎉 MANUAL COMPLETION TRIGGERED!');
            addLog(testData.message);

            ultraDeleteBtn.disabled = false;
            ultraDeleteBtn.textContent = '✅ Deletion Complete!';
            ultraDeleteBtn.style.background = '#28a745';

            // Show completion stats
            addLog(`📊 Final Stats: ${testData.total_deleted} comments deleted successfully`);
            addLog('🎉 All YouTube comments have been removed!');

            // Update the interface to show completion
            const step3 = document.getElementById('step3');
            if (step3) {
                step3.style.background = '#d4edda';
                step3.style.borderLeftColor = '#28a745';
            }

            // Show completion status box
            const completionStatus = document.getElementById('completionStatus');
            const completionStats = document.getElementById('completionStats');

            if (completionStatus && completionStats) {
                completionStatus.style.display = 'block';
                completionStats.textContent = `Total Comments Deleted: ${testData.total_deleted}`;
            }

            // Update page title
            document.title = `✅ Complete - ${testData.total_deleted} Comments Deleted`;

            // Reset button after 5 seconds
            setTimeout(() => {
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                ultraDeleteBtn.style.background = '';
                document.title = 'YouTube Comment Manager';
            }, 5000);
        });

        refreshScreenshotBtn.addEventListener('click', () => {
            refreshScreenshotBtn.disabled = true;
            refreshScreenshotBtn.textContent = '🔄 Refreshing...';
            socket.emit('get_screenshot');

            // Re-enable button after a short delay
            setTimeout(() => {
                refreshScreenshotBtn.disabled = false;
                refreshScreenshotBtn.textContent = '🔄 Refresh';
            }, 2000);
        });

        // Socket events with performance monitoring
        socket.on('connect', () => {
            console.log('🔗 Socket connected at:', new Date().toISOString());
            addLog('🔗 Connected to ultra-fast server');
        });

        socket.on('disconnect', (reason) => {
            console.log('🔌 Socket disconnected:', reason, 'at:', new Date().toISOString());
            addLog(`🔌 Disconnected: ${reason}`);
        });

        socket.on('reconnect', (attemptNumber) => {
            console.log('🔄 Socket reconnected after', attemptNumber, 'attempts at:', new Date().toISOString());
            addLog(`🔄 Reconnected after ${attemptNumber} attempts`);
        });

        socket.on('connect_error', (error) => {
            console.error('❌ Socket connection error:', error, 'at:', new Date().toISOString());
            addLog(`❌ Connection error: ${error.message}`);
        });

        socket.on('browser_ready', (data) => {
            if (data.success) {
                startBtn.textContent = 'Ultra-Fast Browser Started ✓';
                showStep(2);

                if (data.reconnected) {
                    addLog('🔄 Reconnected to existing browser session');
                    addLog('🖥️ Browser was already running - you can continue where you left off');

                    // Re-enable input fields after reconnection
                    setTimeout(() => {
                        ensureInputFieldsResponsive();
                        console.log('Input fields re-enabled after reconnection');
                    }, 500);
                } else {
                    addLog('🚀 Ultra-fast browser ready! Please sign in to YouTube.');
                }

                // Show interactive browser if available
                if (data.interactive_mode && data.screenshot) {
                    showInteractiveBrowser(data.screenshot);
                    addLog('🖥️ Interactive browser mode enabled - you can click and type directly!');
                }

                // Update display with actual settings
                if (data.refresh_interval) {
                    refreshIntervalDisplay.textContent = data.refresh_interval + 's';
                    addLog(`🔄 Auto-refresh: ${data.refresh_interval}s intervals`);
                }
                if (data.stuck_time) {
                    stuckTimeDisplay.textContent = data.stuck_time + 's';
                    addLog(`⏱️ Stuck detection: ${data.stuck_time}s timeout`);
                }
            } else {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('settings_updated', (data) => {
            refreshIntervalDisplay.textContent = data.refresh_interval + 's';
            stuckTimeDisplay.textContent = data.stuck_time + 's';
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`✅ Settings updated: ${data.refresh_interval}s refresh, ${data.stuck_time}s stuck detection`);
        });

        socket.on('settings_error', (data) => {
            updateSettingsBtn.textContent = '🔄 Update Settings';
            updateSettingsBtn.disabled = false;
            addLog(`❌ Settings error: ${data.error}`);
        });

        socket.on('login_ready', (data) => {
            if (data.success) {
                checkLoginBtn.textContent = 'Login Verified ✓';
                showStep(3);
                addLog('✅ Login verified! Ready for ULTRA-FAST deletion.');
            } else {
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                addLog('❌ Login not detected. Please sign in first.');
            }
        });

        socket.on('deletion_status', (data) => {
            if (data.started) {
                addLog('🔥 ULTRA-FAST DELETION STARTED! (4 deletes/second)');
            } else if (data.error) {
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                addLog(`❌ Error: ${data.error}`);
            }
        });

        socket.on('progress_update', (data) => {
            console.log('📝 DEBUG: Progress update received:', data);
            addLog(data.message);
        });

        // Test event handler to verify WebSocket connectivity
        socket.on('test_event', (data) => {
            console.log('🧪 TEST: Test event received:', data);
            addLog('🧪 TEST EVENT RECEIVED FROM SERVER!');
            addLog(`🧪 Message: ${data.message}`);
        });

        socket.on('deletion_complete', (data) => {
            console.log('🎉 DEBUG: Deletion complete event received:', data);
            console.log('🎉 DEBUG: Event type:', typeof data);
            console.log('🎉 DEBUG: Event keys:', Object.keys(data));
            addLog('🎉 WEBSOCKET EVENT RECEIVED!');
            addLog(`🎉 Data: ${JSON.stringify(data)}`);

            // Use the unified completion handler
            handleDeletionCompletion({
                ...data,
                source: 'websocket'
            });

            // Update the interface to show completion
            const step3 = document.getElementById('step3');
            if (step3) {
                step3.style.background = '#d4edda';
                step3.style.borderLeftColor = '#28a745';
            }

            // Show completion status box
            const completionStatus = document.getElementById('completionStatus');
            const completionStats = document.getElementById('completionStats');

            if (completionStatus && completionStats) {
                completionStatus.style.display = 'block';
                completionStats.textContent = `Total Comments Deleted: ${data.total_deleted}`;
            }

            // Update page title
            document.title = `✅ Complete - ${data.total_deleted} Comments Deleted`;

            // Reset page title after 5 seconds
            setTimeout(() => {
                document.title = 'YouTube Comment Manager';
            }, 5000);
        });

        socket.on('deletion_error', (data) => {
            addLog(`❌ ${data.message}`);
            ultraDeleteBtn.disabled = false;
            ultraDeleteBtn.textContent = '❌ Error Occurred';
            ultraDeleteBtn.style.background = '#dc3545';
        });

        socket.on('browser_status', (data) => {
            if (data.closed) {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Ultra-Fast Browser';
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Quick Login Check';
                ultraDeleteBtn.disabled = false;
                ultraDeleteBtn.textContent = '🔥 START ULTRA-FAST DELETION';
                hideStep(2);
                hideStep(3);
                hideInteractiveBrowser();
                addLog('🔴 Browser closed');
            }
        });

        // Interactive browser event handlers
        socket.on('browser_screenshot', (data) => {
            console.log('📸 Received browser_screenshot event:', typeof data, data);

            // Handle both old format (just screenshot) and new format (with context)
            let screenshot, url, title, error;

            if (typeof data === 'string') {
                // Old format - just base64 screenshot
                console.log('📸 Processing old format screenshot');
                screenshot = data;
                updateBrowserScreenshot(screenshot);
            } else if (data && data.screenshot) {
                // New format - screenshot with context and priority handling
                console.log('📸 Processing new format screenshot with URL:', data.url);
                screenshot = data.screenshot;
                url = data.url;
                title = data.title;
                updateBrowserScreenshot(screenshot);

                // Log priority actions and URL changes with enhanced visibility
                if (data.url_changed) {
                    addLog(`🌐 URL CHANGED: ${url}`);
                    if (title && title !== 'Browser Disconnected') {
                        addLog(`📄 Page Title: ${title}`);
                    }
                    console.log('🌐 Priority: URL change detected');
                } else if (data.priority_reason) {
                    const actionIcons = {
                        'click': '🖱️',
                        'type': '⌨️',
                        'auto_action': '🤖',
                        'user_request': '👤'
                    };
                    const icon = actionIcons[data.priority_reason] || '⚡';
                    console.log(`${icon} Priority screenshot: ${data.priority_reason}`);

                    // Only log significant actions to avoid spam
                    if (data.priority_reason === 'click') {
                        addLog(`🖱️ Click action detected - screenshot updated`);
                    } else if (data.priority_reason === 'type') {
                        addLog(`⌨️ Typing action detected - screenshot updated`);
                    }
                }

                // Update browser info with URL context
                const browserInfo = document.getElementById('browserInfo');
                if (browserInfo) {
                    let infoText = `Browser active • ${new Date().toLocaleTimeString()}`;
                    if (url) {
                        const shortUrl = url.length > 50 ? url.substring(0, 50) + '...' : url;
                        infoText += ` • ${shortUrl}`;
                    }
                    if (title && title !== 'Browser Disconnected') {
                        const shortTitle = title.length > 30 ? title.substring(0, 30) + '...' : title;
                        infoText += ` • "${shortTitle}"`;
                    }

                    // Add priority indicator
                    if (data.url_changed) {
                        infoText += ' • 🌐 URL CHANGED';
                    } else if (data.priority_reason) {
                        const actionIcons = {
                            'click': '🖱️',
                            'type': '⌨️',
                            'auto_action': '🤖',
                            'user_request': '👤'
                        };
                        const icon = actionIcons[data.priority_reason] || '⚡';
                        infoText += ` • ${icon} ${data.priority_reason.toUpperCase()}`;
                    }

                    browserInfo.textContent = infoText;
                }
            } else if (data && data.error) {
                // Handle error case
                console.log('📸 Processing error case:', data.error);
                const browserInfo = document.getElementById('browserInfo');
                if (browserInfo) {
                    browserInfo.textContent = `❌ ${data.error} • ${new Date().toLocaleTimeString()}`;
                }
                addLog(`❌ Browser error: ${data.error}`);
            } else {
                console.log('📸 Unknown screenshot data format:', data);
                addLog(`❌ Unknown screenshot data format received`);
            }
        });

        socket.on('click_result', (data) => {
            if (data.success) {
                addLog(`✅ Clicked at (${data.x}, ${data.y})`);
            } else {
                addLog(`❌ Click failed at (${data.x}, ${data.y})`);
            }
        });

        socket.on('type_result', (data) => {
            if (data.success) {
                addLog(`✅ Typed: ${data.text}`);
            } else {
                addLog(`❌ Type failed: ${data.text}`);
            }
        });

        socket.on('signin_found', (data) => {
            findSigninBtn.disabled = false;
            findSigninBtn.textContent = '🎯 Find Sign-In';
            if (data.success) {
                addLog('✅ Sign-In button found and highlighted!');
            } else {
                addLog('❌ Sign-In button not found on this page');
            }
        });

        socket.on('signin_clicked', (data) => {
            autoSigninBtn.disabled = false;
            autoSigninBtn.textContent = '🔑 Auto Click';
            if (data.success) {
                addLog('✅ Sign-In button clicked automatically!');
            } else {
                addLog('❌ Could not click Sign-In button');
            }
        });

        // Interactive browser functions
        function showInteractiveBrowser(screenshot) {
            const browserView = document.getElementById('browserView');
            const browserScreenshot = document.getElementById('browserScreenshot');
            const browserLoading = document.getElementById('browserLoading');

            browserView.style.display = 'block';
            browserLoading.style.display = 'none';
            browserScreenshot.style.display = 'block';
            browserScreenshot.src = `data:image/png;base64,${screenshot}`;

            // Add click handler
            browserScreenshot.onclick = handleBrowserClick;

            // Add keyboard handler for typing
            document.addEventListener('keydown', handleBrowserKeydown);
        }

        function hideInteractiveBrowser() {
            const browserView = document.getElementById('browserView');
            browserView.style.display = 'none';

            // Remove event listeners
            document.removeEventListener('keydown', handleBrowserKeydown);
        }

        function updateBrowserScreenshot(screenshot) {
            console.log('🖼️ updateBrowserScreenshot called with screenshot length:', screenshot ? screenshot.length : 'null');

            const browserScreenshot = document.getElementById('browserScreenshot');
            const browserLoading = document.getElementById('browserLoading');

            if (browserScreenshot && screenshot) {
                console.log('🖼️ Updating browser screenshot element');
                browserLoading.style.display = 'none';
                browserScreenshot.style.display = 'block';

                // Use CSS-based resizing for much faster updates
                browserScreenshot.style.maxWidth = '1000px';
                browserScreenshot.style.maxHeight = '700px';
                browserScreenshot.style.width = 'auto';
                browserScreenshot.style.height = 'auto';

                browserScreenshot.src = `data:image/png;base64,${screenshot}`;
                console.log('🖼️ Screenshot updated successfully with CSS resizing');
            } else {
                console.log('🖼️ Failed to update screenshot - missing element or data');
            }
        }

        function handleBrowserClick(event) {
            event.preventDefault();

            const rect = event.target.getBoundingClientRect();
            const x = Math.round(event.clientX - rect.left);
            const y = Math.round(event.clientY - rect.top);

            // Send display coordinates - backend will handle scaling
            socket.emit('browser_click', { x: x, y: y });
            addLog(`🖱️ Clicking at display coordinates (${x}, ${y})`);

            // Visual feedback
            showClickFeedback(event.clientX, event.clientY);
        }

        function showClickFeedback(x, y) {
            // Create a visual click indicator
            const indicator = document.createElement('div');
            indicator.style.position = 'fixed';
            indicator.style.left = (x - 10) + 'px';
            indicator.style.top = (y - 10) + 'px';
            indicator.style.width = '20px';
            indicator.style.height = '20px';
            indicator.style.borderRadius = '50%';
            indicator.style.border = '2px solid #ff0000';
            indicator.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
            indicator.style.pointerEvents = 'none';
            indicator.style.zIndex = '9999';
            indicator.style.animation = 'clickPulse 0.6s ease-out';

            document.body.appendChild(indicator);

            setTimeout(() => {
                document.body.removeChild(indicator);
            }, 600);
        }

        function handleBrowserKeydown(event) {
            // Only handle typing when browser view is visible
            const browserView = document.getElementById('browserView');
            if (browserView.style.display === 'none') return;

            // Ignore special keys and shortcuts
            if (event.ctrlKey || event.altKey || event.metaKey) return;
            if (event.key.length > 1 && !['Enter', 'Backspace', 'Tab', 'Space'].includes(event.key)) return;

            event.preventDefault();

            let text = event.key;
            if (event.key === 'Enter') text = '\n';
            if (event.key === 'Tab') text = '\t';
            if (event.key === 'Space') text = ' ';
            if (event.key === 'Backspace') text = '\b';

            socket.emit('browser_type', { text: text });
        }

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const p = document.createElement('p');
            p.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(p);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function showStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'block';
            document.getElementById(`step${stepNum}`).classList.add('active');
        }

        function hideStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'none';
            document.getElementById(`step${stepNum}`).classList.remove('active');
        }

        // Navigation functions
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page-section').forEach(page => {
                page.classList.remove('active');
            });

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected page
            document.getElementById(`page-${pageId}`).classList.add('active');

            // Add active class to clicked nav item
            if (event && event.target) {
                event.target.classList.add('active');
            }

            // If showing settings page, ensure input fields are responsive
            if (pageId === 'settings') {
                setTimeout(() => {
                    ensureInputFieldsResponsive();
                    console.log('Settings page shown - input fields re-enabled');
                }, 100);
            }
        }

        // Preset configuration functions
        function setPreset(preset) {
            let refreshVal, stuckVal;

            switch(preset) {
                case 'aggressive':
                    refreshVal = 5;
                    stuckVal = 3;
                    break;
                case 'balanced':
                    refreshVal = 15;
                    stuckVal = 8;
                    break;
                case 'conservative':
                    refreshVal = 30;
                    stuckVal = 15;
                    break;
                default:
                    return;
            }

            refreshInterval.value = refreshVal;
            stuckTime.value = stuckVal;
            refreshValue.textContent = refreshVal + 's';
            stuckValue.textContent = stuckVal + 's';

            addLog(`⚙️ Applied ${preset} preset: ${refreshVal}s refresh, ${stuckVal}s stuck detection`);
        }

        // Credentials Management
        function loadCredentials() {
            const email = localStorage.getItem('ytEmail');
            const password = localStorage.getItem('ytPassword');
            const autoSignIn = localStorage.getItem('autoSignInEnabled') === 'true';
            const rememberCreds = localStorage.getItem('rememberCredentials') !== 'false';

            if (email && rememberCreds) {
                document.getElementById('ytEmail').value = email;
            }
            if (password && rememberCreds) {
                document.getElementById('ytPassword').value = password;
            }
            document.getElementById('autoSignInEnabled').checked = autoSignIn;
            document.getElementById('rememberCredentials').checked = rememberCreds;

            updateCredentialsStatus();
        }

        function saveCredentials() {
            const email = document.getElementById('ytEmail').value;
            const password = document.getElementById('ytPassword').value;
            const autoSignIn = document.getElementById('autoSignInEnabled').checked;
            const rememberCreds = document.getElementById('rememberCredentials').checked;

            if (!email || !password) {
                showCredentialsStatus('Please enter both email and password', 'error');
                return;
            }

            if (rememberCreds) {
                localStorage.setItem('ytEmail', email);
                localStorage.setItem('ytPassword', password);
            } else {
                localStorage.removeItem('ytEmail');
                localStorage.removeItem('ytPassword');
            }

            localStorage.setItem('autoSignInEnabled', autoSignIn);
            localStorage.setItem('rememberCredentials', rememberCreds);

            showCredentialsStatus('Credentials saved successfully!', 'success');
            addLog('🔐 YouTube credentials saved');
        }

        function clearCredentials() {
            localStorage.removeItem('ytEmail');
            localStorage.removeItem('ytPassword');
            localStorage.removeItem('autoSignInEnabled');
            localStorage.setItem('rememberCredentials', 'false');

            document.getElementById('ytEmail').value = '';
            document.getElementById('ytPassword').value = '';
            document.getElementById('autoSignInEnabled').checked = false;
            document.getElementById('rememberCredentials').checked = false;

            showCredentialsStatus('Credentials cleared', 'warning');
            addLog('🗑️ YouTube credentials cleared');
        }

        function testCredentials() {
            const email = document.getElementById('ytEmail').value;
            const password = document.getElementById('ytPassword').value;

            if (!email || !password) {
                showCredentialsStatus('Please enter both email and password', 'error');
                return;
            }

            showCredentialsStatus('Testing credentials...', 'info');
            addLog('🧪 Testing YouTube credentials...');

            // Send credentials to backend for testing
            socket.emit('test_credentials', { email: email, password: password });
        }

        function showCredentialsStatus(message, type) {
            const statusDiv = document.getElementById('credentialsStatus');
            const statusText = document.getElementById('credentialsStatusText');

            statusText.textContent = message;
            statusDiv.style.display = 'block';

            // Set colors based on type
            switch(type) {
                case 'success':
                    statusDiv.style.background = '#d4edda';
                    statusDiv.style.border = '1px solid #c3e6cb';
                    statusDiv.style.color = '#155724';
                    break;
                case 'error':
                    statusDiv.style.background = '#f8d7da';
                    statusDiv.style.border = '1px solid #f5c6cb';
                    statusDiv.style.color = '#721c24';
                    break;
                case 'warning':
                    statusDiv.style.background = '#fff3cd';
                    statusDiv.style.border = '1px solid #ffeaa7';
                    statusDiv.style.color = '#856404';
                    break;
                case 'info':
                    statusDiv.style.background = '#d1ecf1';
                    statusDiv.style.border = '1px solid #bee5eb';
                    statusDiv.style.color = '#0c5460';
                    break;
            }

            // Hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        function updateCredentialsStatus() {
            const email = document.getElementById('ytEmail').value;
            const password = document.getElementById('ytPassword').value;

            if (email && password) {
                showCredentialsStatus('Credentials ready for auto sign-in', 'success');
            }
        }

        function getStoredCredentials() {
            return {
                email: localStorage.getItem('ytEmail'),
                password: localStorage.getItem('ytPassword'),
                autoSignInEnabled: localStorage.getItem('autoSignInEnabled') === 'true'
            };
        }

        function testInputFields() {
            const emailInput = document.getElementById('ytEmail');
            const passwordInput = document.getElementById('ytPassword');

            console.log('Testing input fields...');
            console.log('Email element:', emailInput);
            console.log('Password element:', passwordInput);

            if (emailInput) {
                console.log('Email value:', emailInput.value);
                console.log('Email disabled:', emailInput.disabled);
                console.log('Email readonly:', emailInput.readOnly);

                // Try to focus and set value
                emailInput.focus();
                emailInput.value = '<EMAIL>';
                console.log('Set email to test value:', emailInput.value);
            }

            if (passwordInput) {
                console.log('Password value length:', passwordInput.value.length);
                console.log('Password disabled:', passwordInput.disabled);
                console.log('Password readonly:', passwordInput.readOnly);

                // Try to focus and set value
                passwordInput.focus();
                passwordInput.value = 'testpassword';
                console.log('Set password to test value, length:', passwordInput.value.length);
            }

            showCredentialsStatus('Input fields tested - check console for details', 'info');
        }

        // Function to ensure input fields are always responsive
        function ensureInputFieldsResponsive() {
            const emailInput = document.getElementById('ytEmail');
            const passwordInput = document.getElementById('ytPassword');

            if (emailInput) {
                emailInput.removeAttribute('readonly');
                emailInput.removeAttribute('disabled');
                emailInput.style.pointerEvents = 'auto';
                emailInput.style.userSelect = 'text';
                emailInput.style.background = 'white';
                emailInput.style.color = '#333';

                // Remove any conflicting event listeners and re-add
                emailInput.removeEventListener('input', emailInputHandler);
                emailInput.addEventListener('input', emailInputHandler);
                emailInput.removeEventListener('focus', emailFocusHandler);
                emailInput.addEventListener('focus', emailFocusHandler);
            }

            if (passwordInput) {
                passwordInput.removeAttribute('readonly');
                passwordInput.removeAttribute('disabled');
                passwordInput.style.pointerEvents = 'auto';
                passwordInput.style.userSelect = 'text';
                passwordInput.style.background = 'white';
                passwordInput.style.color = '#333';

                // Remove any conflicting event listeners and re-add
                passwordInput.removeEventListener('input', passwordInputHandler);
                passwordInput.addEventListener('input', passwordInputHandler);
                passwordInput.removeEventListener('focus', passwordFocusHandler);
                passwordInput.addEventListener('focus', passwordFocusHandler);
            }
        }

        // Event handlers for input fields
        function emailInputHandler() {
            console.log('Email input changed:', this.value);
            updateCredentialsStatus();
        }

        function passwordInputHandler() {
            console.log('Password input changed (length):', this.value.length);
            updateCredentialsStatus();
        }

        function emailFocusHandler() {
            console.log('Email field focused');
            this.style.borderColor = '#007bff';
        }

        function passwordFocusHandler() {
            console.log('Password field focused');
            this.style.borderColor = '#007bff';
        }

        // Event listeners for credentials
        document.addEventListener('DOMContentLoaded', function() {
            // Load credentials on page load
            loadCredentials();

            // Ensure input fields are responsive
            ensureInputFieldsResponsive();

            // Add event listeners
            document.getElementById('saveCredentialsBtn').addEventListener('click', saveCredentials);
            document.getElementById('testCredentialsBtn').addEventListener('click', testCredentials);
            document.getElementById('clearCredentialsBtn').addEventListener('click', clearCredentials);
            document.getElementById('testInputBtn').addEventListener('click', testInputFields);

            // Auto-save when checkboxes change
            document.getElementById('autoSignInEnabled').addEventListener('change', function() {
                localStorage.setItem('autoSignInEnabled', this.checked);
            });

            document.getElementById('rememberCredentials').addEventListener('change', function() {
                localStorage.setItem('rememberCredentials', this.checked);
                if (!this.checked) {
                    clearCredentials();
                }
            });

            // Periodic check to ensure input fields remain responsive
            setInterval(() => {
                if (document.getElementById('page-settings').classList.contains('active')) {
                    ensureInputFieldsResponsive();
                }
            }, 5000); // Check every 5 seconds when settings page is active
        });

        // Socket event for credential test results
        socket.on('credentials_test_result', (data) => {
            if (data.success) {
                showCredentialsStatus('✅ Credentials are valid!', 'success');
                addLog('✅ YouTube credentials verified successfully');
            } else {
                showCredentialsStatus('❌ Invalid credentials: ' + data.error, 'error');
                addLog('❌ YouTube credentials test failed: ' + data.error);
            }
        });

        // Socket event for auto sign-in results
        socket.on('signin_result', (data) => {
            if (data.success) {
                // Keep button disabled while we check for comments
                autoSignInWithCredsBtn.textContent = '🔍 Checking Comments...';
                autoSignInWithCredsBtn.style.background = '#ffc107';
                addLog('✅ Successfully signed in with saved credentials!');
                addLog('🔍 Automatically checking for YouTube Comments section...');
            } else {
                autoSignInWithCredsBtn.disabled = false;
                autoSignInWithCredsBtn.textContent = '❌ Sign-In Failed';
                autoSignInWithCredsBtn.style.background = '#dc3545';
                addLog('❌ Auto sign-in failed: ' + (data.error || 'Unknown error'));

                // Reset button after 3 seconds
                setTimeout(() => {
                    autoSignInWithCredsBtn.textContent = '🚀 Auto Sign-In';
                    autoSignInWithCredsBtn.style.background = '';
                }, 3000);
            }
        });

        // Socket event for automatic comments detection after sign-in
        socket.on('auto_comments_ready', (data) => {
            // Always re-enable and reset the auto sign-in button
            autoSignInWithCredsBtn.disabled = false;

            if (data.success) {
                // Success - show completion
                autoSignInWithCredsBtn.textContent = '✅ Complete!';
                autoSignInWithCredsBtn.style.background = '#28a745';

                showStep(3);
                addLog('🎯 Found "Your YouTube Comments" section automatically!');
                addLog('✅ Ready for ultra-fast comment deletion!');

                // Update login check button to show success
                checkLoginBtn.textContent = 'Auto-Verified ✓';
                checkLoginBtn.style.background = '#28a745';

                // Reset auto sign-in button after 3 seconds
                setTimeout(() => {
                    autoSignInWithCredsBtn.textContent = '🚀 Auto Sign-In';
                    autoSignInWithCredsBtn.style.background = '';
                }, 3000);
            } else {
                // Failed to find comments - show warning
                autoSignInWithCredsBtn.textContent = '⚠️ Manual Check Needed';
                autoSignInWithCredsBtn.style.background = '#ffc107';

                addLog('⚠️ "Your YouTube Comments" section not found automatically');
                addLog('💡 You may need to manually verify or refresh the page');

                // Reset auto sign-in button after 3 seconds
                setTimeout(() => {
                    autoSignInWithCredsBtn.textContent = '🚀 Auto Sign-In';
                    autoSignInWithCredsBtn.style.background = '';
                }, 3000);

                // Still enable login check button for manual verification
                setTimeout(() => {
                    checkLoginBtn.click();
                }, 1000);
            }
        });
    </script>
</body>
</html>
