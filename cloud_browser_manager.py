import os
import time
import base64
import threading
from io import BytesIO
from PIL import Image
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class CloudBrowserManager:
    def __init__(self, progress_callback=None):
        self.driver = None
        self.progress_callback = progress_callback
        self.screenshot_thread = None
        self.is_running = False
        self.current_screenshot = None
        
    def setup_cloud_browser(self):
        """Setup browser for cloud environment"""
        try:
            self._update_progress("Setting up cloud browser environment...")
            
            # Setup virtual display for headless environment
            if os.name != 'nt':  # Not Windows
                from pyvirtualdisplay import Display
                self.display = Display(visible=0, size=(1920, 1080))
                self.display.start()
                self._update_progress("Virtual display started")
            
            # Chrome options for cloud deployment
            chrome_options = ChromeOptions()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # For cloud environments, we might need these additional options
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-images")  # Faster loading
            chrome_options.add_argument("--disable-javascript")  # We'll enable selectively
            
            # Setup ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_window_size(1920, 1080)
            
            # Remove automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self._update_progress("Cloud browser ready!")
            return True
            
        except Exception as e:
            self._update_progress(f"Failed to setup cloud browser: {str(e)}")
            return False
    
    def navigate_to_youtube(self):
        """Navigate to YouTube and prepare for user interaction"""
        try:
            self._update_progress("Navigating to YouTube...")
            self.driver.get("https://www.youtube.com")
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Start screenshot streaming for user interaction
            self.start_screenshot_streaming()
            
            self._update_progress("YouTube loaded. Please log in using the browser view below.")
            return True
            
        except Exception as e:
            self._update_progress(f"Failed to navigate to YouTube: {str(e)}")
            return False
    
    def start_screenshot_streaming(self):
        """Start streaming browser screenshots for user interaction"""
        self.is_running = True
        self.screenshot_thread = threading.Thread(target=self._screenshot_loop)
        self.screenshot_thread.daemon = True
        self.screenshot_thread.start()
    
    def _screenshot_loop(self):
        """Continuously capture browser screenshots"""
        while self.is_running and self.driver:
            try:
                # Capture screenshot
                screenshot = self.driver.get_screenshot_as_png()
                
                # Convert to base64 for web transmission
                screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')
                self.current_screenshot = screenshot_b64
                
                # Update every 2 seconds to reduce load
                time.sleep(2)
                
            except Exception as e:
                print(f"Screenshot error: {e}")
                time.sleep(5)
    
    def get_current_screenshot(self):
        """Get the current browser screenshot as base64"""
        return self.current_screenshot
    
    def click_at_coordinates(self, x, y):
        """Simulate click at specific coordinates"""
        try:
            # Use JavaScript to click at coordinates
            self.driver.execute_script(f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    element.click();
                }}
            """)
            self._update_progress(f"Clicked at coordinates ({x}, {y})")
            return True
        except Exception as e:
            self._update_progress(f"Click failed: {str(e)}")
            return False
    
    def type_text(self, text):
        """Type text into the currently focused element"""
        try:
            # Find the active element and type
            active_element = self.driver.switch_to.active_element
            active_element.send_keys(text)
            self._update_progress(f"Typed text: {text[:20]}...")
            return True
        except Exception as e:
            self._update_progress(f"Typing failed: {str(e)}")
            return False
    
    def scroll_page(self, direction="down", amount=500):
        """Scroll the page"""
        try:
            if direction == "down":
                self.driver.execute_script(f"window.scrollBy(0, {amount});")
            else:
                self.driver.execute_script(f"window.scrollBy(0, -{amount});")
            return True
        except Exception as e:
            self._update_progress(f"Scroll failed: {str(e)}")
            return False
    
    def is_logged_in(self):
        """Check if user is logged into YouTube"""
        try:
            # Look for avatar/profile picture
            avatar = self.driver.find_elements(By.CSS_SELECTOR, "button[aria-label*='Account menu']")
            return len(avatar) > 0
        except:
            return False
    
    def find_and_delete_comments_cloud(self):
        """Cloud-optimized comment deletion"""
        try:
            self._update_progress("Starting comment deletion process...")
            
            # Navigate to Google My Activity
            self.driver.get("https://myactivity.google.com/myactivity?product=YouTube")
            time.sleep(5)
            
            deleted_count = 0
            
            while True:
                # Find comment activities
                comment_elements = self.driver.find_elements(
                    By.XPATH, 
                    "//div[contains(text(), 'Commented on') or contains(text(), 'comment')]"
                )
                
                if not comment_elements:
                    self._update_progress("No more comments found to delete.")
                    break
                
                for comment_element in comment_elements[:5]:  # Process in batches
                    try:
                        # Find delete button
                        parent_div = comment_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'activity')]")
                        delete_button = parent_div.find_element(By.XPATH, ".//button[@aria-label='Delete']")
                        
                        # Click delete
                        self.driver.execute_script("arguments[0].click();", delete_button)
                        time.sleep(1)
                        
                        # Confirm deletion
                        confirm_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Delete') or contains(text(), 'Remove')]"))
                        )
                        confirm_button.click()
                        
                        deleted_count += 1
                        self._update_progress(f"Deleted comment #{deleted_count}")
                        
                        time.sleep(2)  # Rate limiting
                        
                    except Exception as e:
                        continue
                
                # Scroll to load more
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)
            
            self._update_progress(f"Deletion completed! Total deleted: {deleted_count}")
            
        except Exception as e:
            self._update_progress(f"Error during deletion: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        self.is_running = False
        
        if self.screenshot_thread:
            self.screenshot_thread.join(timeout=5)
        
        if self.driver:
            self.driver.quit()
            self.driver = None
        
        if hasattr(self, 'display'):
            self.display.stop()
    
    def _update_progress(self, message):
        """Update progress via callback"""
        print(f"[CloudBrowser] {message}")
        if self.progress_callback:
            self.progress_callback(message)
