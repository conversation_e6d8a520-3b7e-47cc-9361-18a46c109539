#!/usr/bin/env python3
"""
Test script to verify browser setup works correctly
"""

import sys
import os
from cloud_browser_manager import CloudBrowserManager

def test_cloud_browser():
    """Test cloud browser setup"""
    print("Testing Cloud Browser Setup...")
    print("=" * 50)
    
    def progress_callback(message):
        print(f"[TEST] {message}")
    
    # Create browser manager
    browser = CloudBrowserManager(progress_callback=progress_callback)
    
    try:
        # Test browser setup
        print("\n1. Testing browser setup...")
        if browser.setup_cloud_browser():
            print("✅ Browser setup successful!")
            
            # Test navigation
            print("\n2. Testing YouTube navigation...")
            if browser.navigate_to_youtube():
                print("✅ YouTube navigation successful!")
                
                # Test screenshot
                print("\n3. Testing screenshot capture...")
                import time
                time.sleep(3)  # Wait for page to load
                
                screenshot = browser.get_current_screenshot()
                if screenshot:
                    print("✅ Screenshot capture successful!")
                    print(f"Screenshot size: {len(screenshot)} characters")
                else:
                    print("❌ Screenshot capture failed!")
                
            else:
                print("❌ YouTube navigation failed!")
        else:
            print("❌ Browser setup failed!")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        
    finally:
        # Cleanup
        print("\n4. Cleaning up...")
        browser.cleanup()
        print("✅ Cleanup completed!")

def test_local_browser():
    """Test local browser setup"""
    print("\nTesting Local Browser Setup...")
    print("=" * 50)
    
    try:
        from youtube_comment_deleter import YouTubeCommentDeleter
        
        def progress_callback(message):
            print(f"[LOCAL] {message}")
        
        deleter = YouTubeCommentDeleter(progress_callback=progress_callback)
        
        print("\n1. Testing local browser setup...")
        deleter.setup_driver(headless=True)  # Use headless for testing
        
        if deleter.driver:
            print("✅ Local browser setup successful!")
            
            # Test navigation
            print("\n2. Testing YouTube navigation...")
            deleter.driver.get("https://www.youtube.com")
            
            if "YouTube" in deleter.driver.title:
                print("✅ YouTube navigation successful!")
            else:
                print("❌ YouTube navigation failed!")
                
        else:
            print("❌ Local browser setup failed!")
            
    except Exception as e:
        print(f"❌ Local test failed with error: {str(e)}")
        
    finally:
        # Cleanup
        print("\n3. Cleaning up...")
        try:
            if 'deleter' in locals() and deleter.driver:
                deleter.cleanup()
            print("✅ Local cleanup completed!")
        except:
            pass

if __name__ == "__main__":
    print("YouTube Comment Deleter - Browser Test")
    print("=" * 60)
    
    # Test cloud browser
    test_cloud_browser()
    
    print("\n" + "=" * 60)
    
    # Test local browser
    test_local_browser()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    
    input("\nPress Enter to exit...")
