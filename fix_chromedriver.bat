@echo off
echo Fixing ChromeDriver compatibility issues...
echo.

echo Clearing ChromeDriver cache...
if exist "%USERPROFILE%\.wdm" (
    rmdir /s /q "%USERPROFILE%\.wdm"
    echo Cache cleared successfully.
) else (
    echo No cache found to clear.
)

echo.
echo Reinstalling webdriver-manager...
pip uninstall webdriver-manager -y
pip install webdriver-manager

echo.
echo ChromeDriver fix completed!
echo You can now run the application with: python app.py
echo.
pause
