from flask import Flask, render_template, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import threading
from simple_comment_deleter import SimpleCommentDeleter

app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables
comment_deleter = None
deletion_thread = None

@app.route('/')
def index():
    return render_template('simple_interface.html')

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('status', {'message': 'Connected to server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('start_browser')
def handle_start_browser():
    global comment_deleter
    try:
        comment_deleter = SimpleCommentDeleter(progress_callback=send_progress_update)
        
        if comment_deleter.setup_driver(headless=False):
            if comment_deleter.go_to_activity_page():
                emit('browser_ready', {'success': True})
            else:
                emit('browser_ready', {'success': False, 'error': 'Failed to navigate to activity page'})
        else:
            emit('browser_ready', {'success': False, 'error': 'Failed to setup browser'})
    except Exception as e:
        emit('browser_ready', {'success': False, 'error': str(e)})

@socketio.on('check_login')
def handle_check_login():
    global comment_deleter
    if comment_deleter:
        if comment_deleter.wait_for_login():
            emit('login_ready', {'success': True})
        else:
            emit('login_ready', {'success': False})
    else:
        emit('login_ready', {'success': False, 'error': 'Browser not started'})

@socketio.on('start_deletion')
def handle_start_deletion():
    global comment_deleter, deletion_thread
    
    if not comment_deleter:
        emit('deletion_status', {'error': 'Browser not initialized'})
        return
    
    if deletion_thread and deletion_thread.is_alive():
        emit('deletion_status', {'error': 'Deletion already in progress'})
        return
    
    def deletion_worker():
        try:
            comment_deleter.find_and_delete_comments()
        except Exception as e:
            send_progress_update(f"Error: {str(e)}")
    
    deletion_thread = threading.Thread(target=deletion_worker)
    deletion_thread.daemon = True
    deletion_thread.start()
    
    emit('deletion_status', {'started': True})

@socketio.on('close_browser')
def handle_close_browser():
    global comment_deleter
    if comment_deleter:
        comment_deleter.cleanup()
        comment_deleter = None
        emit('browser_status', {'closed': True})

def send_progress_update(message):
    """Send progress update to all connected clients"""
    socketio.emit('progress_update', {'message': message})

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy', 'version': 'simple'})

if __name__ == '__main__':
    print("Starting Simple YouTube Comment Deleter...")
    print("Open your browser and go to: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
