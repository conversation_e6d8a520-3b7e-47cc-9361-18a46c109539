<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Comment Deleter - Cloud Edition</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='cloud_style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🌐 YouTube Comment Deleter - Cloud Edition</h1>
            <p>Delete all your YouTube comments through our cloud browser interface</p>
        </header>

        <div class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                <div class="step active" id="step1">
                    <h3>Step 1: Start Cloud Browser</h3>
                    <p>Launch a browser session in our cloud environment</p>
                    <button id="startCloudBrowserBtn" class="btn btn-primary">🚀 Start Cloud Browser</button>
                </div>

                <div class="step" id="step2" style="display: none;">
                    <h3>Step 2: Login to YouTube</h3>
                    <p>Use the browser view below to log into your YouTube account</p>
                    <div class="login-status">
                        <span id="loginStatus">Waiting for login...</span>
                        <button id="checkLoginBtn" class="btn btn-secondary">Check Login Status</button>
                    </div>
                </div>

                <div class="step" id="step3" style="display: none;">
                    <h3>Step 3: Start Deletion</h3>
                    <p>Begin the automated comment deletion process</p>
                    <div class="deletion-controls">
                        <button id="startDeletionBtn" class="btn btn-danger">🗑️ Delete All Comments</button>
                        <button id="stopDeletionBtn" class="btn btn-warning" style="display: none;">⏹️ Stop</button>
                    </div>
                </div>
            </div>

            <!-- Browser View -->
            <div class="browser-container" id="browserContainer" style="display: none;">
                <div class="browser-header">
                    <h3>🌐 Cloud Browser - YouTube</h3>
                    <div class="browser-controls">
                        <button id="refreshBtn" class="btn btn-small">🔄 Refresh</button>
                        <button id="fullscreenBtn" class="btn btn-small">⛶ Fullscreen</button>
                    </div>
                </div>
                
                <div class="browser-viewport">
                    <img id="browserScreen" src="" alt="Browser View" />
                    <div class="browser-overlay" id="browserOverlay"></div>
                </div>
                
                <div class="browser-toolbar">
                    <input type="text" id="textInput" placeholder="Type text here..." />
                    <button id="typeBtn" class="btn btn-small">Type</button>
                    <button id="scrollUpBtn" class="btn btn-small">↑ Scroll Up</button>
                    <button id="scrollDownBtn" class="btn btn-small">↓ Scroll Down</button>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section" id="progressSection" style="display: none;">
            <h3>📊 Progress</h3>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText">Ready to start...</div>
        </div>

        <!-- Activity Log -->
        <div class="log-container">
            <h3>📋 Activity Log</h3>
            <div id="logOutput" class="log-output">
                <p>Welcome to YouTube Comment Deleter Cloud Edition!</p>
                <p>Click "Start Cloud Browser" to begin.</p>
            </div>
            <button id="clearLogBtn" class="btn btn-secondary">Clear Log</button>
        </div>

        <!-- Footer Controls -->
        <div class="footer-controls">
            <button id="closeBrowserBtn" class="btn btn-secondary">Close Browser Session</button>
            <div class="status-indicators">
                <span id="connectionStatus" class="status-indicator">🔴 Disconnected</span>
                <span id="browserStatus" class="status-indicator">⚫ No Browser</span>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>⚠️ Final Confirmation</h3>
            <p>Are you absolutely sure you want to delete ALL your YouTube comments?</p>
            <p><strong>This action cannot be undone!</strong></p>
            <div class="modal-buttons">
                <button id="confirmDeleteBtn" class="btn btn-danger">Yes, Delete All Comments</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='cloud_script.js') }}"></script>
</body>
</html>
