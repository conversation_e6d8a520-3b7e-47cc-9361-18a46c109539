# YouTube Comment Deleter

A web-based tool that uses Selenium automation to delete all your YouTube comments with one click.

## ⚠️ Important Warning

**This tool will permanently delete ALL your YouTube comments. This action cannot be undone!**

Please make sure you want to proceed before using this tool.

## Features

- 🌐 Web-based interface for easy use
- 🤖 Automated comment deletion using Selenium
- 📊 Real-time progress tracking
- ⏸️ Pause/resume functionality
- 🔒 Secure - runs locally on your machine
- 📱 Responsive design for mobile and desktop

## Prerequisites

- Python 3.7 or higher
- Google Chrome browser
- Internet connection

## Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd YT-Manager
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Chrome is installed**
   - The tool will automatically download the appropriate ChromeDriver

## Usage

1. **Start the application**
   ```bash
   python app.py
   ```

2. **Open your web browser**
   - Navigate to `http://localhost:5000`

3. **Follow the step-by-step process:**
   - **Step 1:** Click "Start Browser" to launch Chrome
   - **Step 2:** Log in to your YouTube account in the opened browser
   - **Step 3:** Click "Delete All Comments" and confirm
   - **Step 4:** Monitor the progress as comments are deleted

## How It Works

1. **Browser Automation:** Uses Selenium WebDriver to control Chrome
2. **YouTube Navigation:** Navigates to Google My Activity for YouTube
3. **Comment Detection:** Finds comment activities in your history
4. **Automated Deletion:** Clicks delete buttons and confirms deletions
5. **Progress Tracking:** Reports progress back to the web interface

## Safety Features

- **Manual Login:** You must log in manually (no password storage)
- **Confirmation Dialog:** Double confirmation before starting deletion
- **Stop Button:** Ability to stop the process at any time
- **Rate Limiting:** Delays between deletions to avoid triggering limits
- **Error Handling:** Graceful handling of network issues and errors

## Technical Details

### Architecture
- **Backend:** Flask web server with Socket.IO for real-time communication
- **Frontend:** HTML/CSS/JavaScript with responsive design
- **Automation:** Selenium WebDriver with Chrome
- **Communication:** WebSocket for real-time progress updates

### File Structure
```
YT Manager/
├── app.py                     # Flask web server
├── youtube_comment_deleter.py # Selenium automation logic
├── requirements.txt           # Python dependencies
├── README.md                 # This file
├── templates/
│   └── index.html            # Web interface
└── static/
    ├── style.css             # Styling
    └── script.js             # Frontend JavaScript
```

## Troubleshooting

### Quick Fixes

1. **ChromeDriver Issues:** The app automatically detects and fixes ChromeDriver compatibility problems
2. **Browser Fallback:** If Chrome fails, the app automatically tries Firefox
3. **Detailed Help:** See `TROUBLESHOOTING.md` for comprehensive solutions

### Common Issues

1. **Browser doesn't start**
   - App will try Chrome first, then Firefox automatically
   - Ensure at least one browser is installed and updated
   - Try running as administrator if needed

2. **Login not detected**
   - Make sure you're fully logged in to YouTube
   - Look for your profile picture in the top right
   - Try refreshing the YouTube page

3. **Comments not found**
   - Check Google My Activity for YouTube comment activities
   - Some older comments might not be visible
   - Privacy settings may affect comment visibility

4. **Deletion stops unexpectedly**
   - YouTube may be rate limiting - wait 10-15 minutes
   - Check internet connection stability
   - Review the activity log for specific errors

### Error Messages

- **"Browser not initialized":** Click "Start Browser" first
- **"Login timeout":** Restart the browser and log in faster
- **"No more comments found":** All visible comments have been processed
- **"WinError 193":** ChromeDriver compatibility issue (auto-fixed)

For detailed troubleshooting steps, see **TROUBLESHOOTING.md**

## Limitations

- Only deletes comments visible in Google My Activity
- May not catch all comments due to YouTube's activity logging
- Subject to YouTube's rate limiting
- Requires manual login for security

## Privacy & Security

- **No data collection:** All processing happens locally
- **No password storage:** You log in manually through the browser
- **No external connections:** Only connects to YouTube/Google services
- **Open source:** You can review all code

## Legal Disclaimer

This tool is for educational purposes and personal use only. Users are responsible for complying with YouTube's Terms of Service and applicable laws. The developers are not responsible for any consequences of using this tool.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is open source. Please use responsibly.

---

**Remember: This tool permanently deletes your comments. Use with caution!**
