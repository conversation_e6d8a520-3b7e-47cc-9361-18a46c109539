<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple YouTube Comment Deleter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .step.active {
            border-left-color: #28a745;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            font-size: 14px;
        }
        
        .warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🗑️ Simple YouTube Comment Deleter</h1>
            <p>Delete all your YouTube comments directly from Google My Activity</p>
        </header>

        <div class="content">
            <div class="warning">
                <h3>⚠️ Warning</h3>
                <p><strong>This will permanently delete ALL your YouTube comments!</strong></p>
                <p>This action cannot be undone!</p>
            </div>

            <div class="step active" id="step1">
                <h3>Step 1: Start Browser</h3>
                <p>Click to open a browser window and navigate to Google My Activity</p>
                <button id="startBtn" class="btn btn-primary">Start Browser</button>
            </div>

            <div class="step" id="step2" style="display: none;">
                <h3>Step 2: Sign In</h3>
                <p>Sign in to your Google account in the opened browser window, then click "Check Login"</p>
                <button id="checkLoginBtn" class="btn btn-primary">Check Login</button>
            </div>

            <div class="step" id="step3" style="display: none;">
                <h3>Step 3: Delete Comments</h3>
                <p>Ready to delete all your YouTube comments</p>
                <button id="deleteBtn" class="btn btn-danger">🗑️ Delete All Comments</button>
            </div>

            <div class="log-output" id="logOutput">
                <p>Welcome! Click "Start Browser" to begin.</p>
            </div>

            <button id="closeBtn" class="btn btn-secondary">Close Browser</button>
        </div>
    </div>

    <script>
        const socket = io();
        
        const startBtn = document.getElementById('startBtn');
        const checkLoginBtn = document.getElementById('checkLoginBtn');
        const deleteBtn = document.getElementById('deleteBtn');
        const closeBtn = document.getElementById('closeBtn');
        const logOutput = document.getElementById('logOutput');
        
        // Event listeners
        startBtn.addEventListener('click', () => {
            startBtn.disabled = true;
            startBtn.textContent = 'Starting...';
            socket.emit('start_browser');
        });
        
        checkLoginBtn.addEventListener('click', () => {
            checkLoginBtn.disabled = true;
            checkLoginBtn.textContent = 'Checking...';
            socket.emit('check_login');
        });
        
        deleteBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to delete ALL your YouTube comments? This cannot be undone!')) {
                deleteBtn.disabled = true;
                deleteBtn.textContent = 'Deleting...';
                socket.emit('start_deletion');
            }
        });
        
        closeBtn.addEventListener('click', () => {
            socket.emit('close_browser');
        });
        
        // Socket events
        socket.on('connect', () => {
            addLog('Connected to server');
        });
        
        socket.on('browser_ready', (data) => {
            if (data.success) {
                startBtn.textContent = 'Browser Started ✓';
                showStep(2);
                addLog('Browser started. Please sign in to Google in the opened window.');
            } else {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Browser';
                addLog(`Error: ${data.error}`);
            }
        });
        
        socket.on('login_ready', (data) => {
            if (data.success) {
                checkLoginBtn.textContent = 'Login Successful ✓';
                showStep(3);
                addLog('Login successful! Ready to delete comments.');
            } else {
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Check Login';
                addLog('Login not detected. Please sign in first.');
            }
        });
        
        socket.on('deletion_status', (data) => {
            if (data.started) {
                addLog('Comment deletion started...');
            } else if (data.error) {
                deleteBtn.disabled = false;
                deleteBtn.textContent = '🗑️ Delete All Comments';
                addLog(`Error: ${data.error}`);
            }
        });
        
        socket.on('progress_update', (data) => {
            addLog(data.message);
            if (data.message.includes('completed')) {
                deleteBtn.disabled = false;
                deleteBtn.textContent = 'Deletion Complete ✓';
            }
        });
        
        socket.on('browser_status', (data) => {
            if (data.closed) {
                startBtn.disabled = false;
                startBtn.textContent = 'Start Browser';
                checkLoginBtn.disabled = false;
                checkLoginBtn.textContent = 'Check Login';
                deleteBtn.disabled = false;
                deleteBtn.textContent = '🗑️ Delete All Comments';
                hideStep(2);
                hideStep(3);
                addLog('Browser closed');
            }
        });
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const p = document.createElement('p');
            p.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(p);
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        function showStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'block';
            document.getElementById(`step${stepNum}`).classList.add('active');
        }
        
        function hideStep(stepNum) {
            document.getElementById(`step${stepNum}`).style.display = 'none';
            document.getElementById(`step${stepNum}`).classList.remove('active');
        }
    </script>
</body>
</html>
