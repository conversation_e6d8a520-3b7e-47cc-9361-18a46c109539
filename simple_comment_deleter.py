import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class SimpleCommentDeleter:
    def __init__(self, progress_callback=None):
        self.driver = None
        self.progress_callback = progress_callback
        self.deleted_count = 0

        # Auto-refresh settings
        self.refresh_interval = 120  # Refresh every 2 minutes (more conservative)
        self.max_stuck_time = 45     # Consider stuck after 45 seconds of no progress
        self.last_progress_time = time.time()
        self.last_deleted_count = 0

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver optimized for Google login"""
        try:
            chrome_options = ChromeOptions()

            # Create a temporary profile directory to avoid conflicts
            import tempfile
            import os
            temp_profile = tempfile.mkdtemp()
            chrome_options.add_argument(f"--user-data-dir={temp_profile}")

            # Anti-detection measures
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Set a realistic user agent
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Options for better Google compatibility
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-translate")

            # Enable features that Google expects
            chrome_options.add_argument("--enable-features=VizDisplayCompositor")

            if headless:
                chrome_options.add_argument("--headless")

            # Get ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Remove webdriver property and add realistic properties
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
            """)

            self._update_progress("Browser started successfully!")
            return True

        except Exception as e:
            self._update_progress(f"Failed to start browser: {str(e)}")
            return False

    def go_to_activity_page(self):
        """Navigate to Google first, then to YouTube activity page"""
        try:
            # First go to Google to establish a session
            self._update_progress("Navigating to Google...")
            self.driver.get("https://www.google.com")
            self.driver.maximize_window()

            # Wait for Google to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            time.sleep(2)  # Give it a moment

            # Now navigate to the YouTube comments page
            url = "https://myactivity.google.com/page?page=youtube_comments&continue=https://myactivity.google.com/product/youtube/interactions?hl%3Den&utm_source=youtube&utm_medium=web&hl=en"
            self._update_progress("Navigating to YouTube Comments page...")

            self.driver.get(url)

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            self._update_progress("YouTube Comments page loaded. Please sign in if needed.")
            self._update_progress("If you see 'This browser or app may not be secure', try:")
            self._update_progress("1. Click 'Try using a different browser'")
            self._update_progress("2. Or use the 'Sign in with Google' option")
            self._update_progress("3. Or manually navigate to the comments page")

            return True

        except Exception as e:
            self._update_progress(f"Failed to navigate to activity page: {str(e)}")
            return False

    def wait_for_login(self):
        """Wait for user to complete login"""
        self._update_progress("Waiting for login... Please sign in to your Google account.")

        # Wait for the activity page to be fully loaded (indicates successful login)
        try:
            # Look for activity items or the main content area
            WebDriverWait(self.driver, 300).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("Login successful! Ready to delete comments.")
            return True
        except TimeoutException:
            self._update_progress("Login timeout. Please try again.")
            return False

    def _is_logged_in(self):
        """Check if user is logged in by looking for activity content"""
        try:
            # Look for the main activity content or any activity items
            activity_indicators = [
                "//div[@data-value='YouTube']",  # YouTube filter
                "//div[contains(@class, 'activity')]",  # Activity items
                "//c-wiz",  # Main content wrapper
                "//div[contains(text(), 'YouTube')]"  # Any YouTube text
            ]

            for selector in activity_indicators:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    return True
            return False
        except:
            return False

    def find_and_delete_comments(self):
        """Find and delete YouTube comments from activity page with auto-refresh"""
        try:
            self._update_progress("Starting comment deletion process...")
            self._update_progress(f"🔄 Auto-refresh enabled: every {self.refresh_interval}s or when stuck for {self.max_stuck_time}s")

            start_time = time.time()
            self.last_progress_time = start_time

            while True:
                cycle_start = time.time()
                initial_count = self.deleted_count

                # Find comment activities - look for various comment indicators
                comment_selectors = [
                    "//div[contains(text(), 'Commented on')]",
                    "//div[contains(text(), 'comment')]",
                    "//div[contains(text(), 'Comment')]",
                    "//span[contains(text(), 'Commented on')]",
                    "//span[contains(text(), 'comment')]"
                ]

                comment_elements = []
                for selector in comment_selectors:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    comment_elements.extend(elements)

                if not comment_elements:
                    # No comments found, check if we should refresh
                    if self._should_refresh():
                        self._refresh_page()
                        continue
                    else:
                        self._update_progress("No more comments found to delete.")
                        break

                self._update_progress(f"Found {len(comment_elements)} comment activities")

                # Process each comment
                for i, comment_element in enumerate(comment_elements[:5]):  # Process in batches
                    try:
                        self._delete_single_comment(comment_element)
                        self._update_progress_tracking()  # Update progress tracking
                        time.sleep(2)  # Rate limiting
                    except Exception as e:
                        self.logger.warning(f"Failed to delete comment {i+1}: {str(e)}")
                        continue

                # Check if we made progress this cycle
                cycle_deleted = self.deleted_count - initial_count
                if cycle_deleted == 0:
                    # No progress made, check if we need to refresh
                    if self._should_refresh():
                        self._refresh_page()
                        continue

                # Check for time-based refresh
                if time.time() - cycle_start > self.refresh_interval:
                    self._update_progress(f"⏰ {self.refresh_interval}s interval reached - refreshing page")
                    self._refresh_page()
                    continue

                # Scroll down to load more activities
                self._scroll_to_load_more()
                time.sleep(3)

            self._update_progress(f"Deletion completed! Total comments deleted: {self.deleted_count}")

        except Exception as e:
            self._update_progress(f"Error during deletion process: {str(e)}")

    def _should_refresh(self):
        """Check if page should be refreshed due to being stuck"""
        current_time = time.time()
        time_since_progress = current_time - self.last_progress_time

        if time_since_progress > self.max_stuck_time:
            self._update_progress(f"⚠️ No progress for {time_since_progress:.1f}s - page may be stuck")
            return True

        return False

    def _update_progress_tracking(self):
        """Update progress tracking variables"""
        self.last_progress_time = time.time()
        self.last_deleted_count = self.deleted_count

    def _refresh_page(self):
        """Refresh the page and wait for it to load"""
        try:
            self._update_progress("🔄 Refreshing page to continue deletion...")

            # Store current URL
            current_url = self.driver.current_url

            # Refresh the page
            self.driver.refresh()

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Additional wait for dynamic content
            time.sleep(5)

            # Verify we're still on the right page
            if "myactivity.google.com" not in self.driver.current_url:
                self._update_progress("⚠️ Page changed after refresh, navigating back...")
                self.driver.get(current_url)
                time.sleep(5)

            self._update_progress("✅ Page refreshed successfully - continuing deletion")
            self._update_progress_tracking()  # Reset stuck timer

        except Exception as e:
            self._update_progress(f"❌ Error refreshing page: {str(e)}")
            # Try to navigate back to the comments page
            try:
                self.go_to_activity_page()
            except:
                pass

    def _delete_single_comment(self, comment_element):
        """Delete a single comment activity"""
        try:
            # Scroll to the comment element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", comment_element)
            time.sleep(1)

            # Find the parent container that holds the delete button
            parent_container = comment_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'activity') or contains(@role, 'listitem') or @data-ved][1]")

            # Look for delete button - target the specific X button SVG structure, avoid hamburger menu
            x_button_path = "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
            hamburger_menu_path = "M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"

            delete_selectors = [
                # Target the exact X button SVG structure, excluding hamburger menu
                f".//svg[@width='24'][@height='24'][@viewBox='0 0 24 24']/path[@d='{x_button_path}' and not(@d='{hamburger_menu_path}')]/ancestor::button[1]",
                f".//svg[contains(@class, 'TjcpUd')]/path[@d='{x_button_path}' and not(@d='{hamburger_menu_path}')]/ancestor::button[1]",
                f".//svg[contains(@class, 'NMm5M')]/path[@d='{x_button_path}' and not(@d='{hamburger_menu_path}')]/ancestor::button[1]",
                f".//path[@d='{x_button_path}' and not(@d='{hamburger_menu_path}')]/ancestor::button[1]",  # Direct path match

                # More general X button patterns, avoiding hamburger menu
                ".//svg[@width='24'][@height='24'][@viewBox='0 0 24 24']//path[contains(@d, '19 6.41') and not(contains(@d, 'M3 18h18v-2H3v2z'))]/ancestor::button[1]",
                ".//svg[contains(@class, 'TjcpUd')]//path[contains(@d, '19 6.41') and not(contains(@d, 'M3 18h18v-2H3v2z'))]/ancestor::button[1]",
                ".//svg[contains(@class, 'NMm5M')]//path[contains(@d, '19 6.41') and not(contains(@d, 'M3 18h18v-2H3v2z'))]/ancestor::button[1]",

                # Traditional delete button selectors
                ".//button[@aria-label='Delete']",
                ".//button[contains(@aria-label, 'Delete')]",
                ".//button[contains(@aria-label, 'Remove')]",
                ".//div[@role='button' and contains(@aria-label, 'Delete')]",
                ".//div[@role='button' and contains(@aria-label, 'Remove')]",
                ".//button[contains(text(), 'Delete')]",
                ".//span[contains(text(), 'Delete')]/ancestor::button[1]",

                # Any button containing the X SVG, avoiding hamburger menu
                ".//button[.//svg[@width='24'][@height='24'][@viewBox='0 0 24 24']//path[contains(@d, '19 6.41') and not(contains(@d, 'M3 18h18v-2H3v2z'))]]"
            ]

            delete_button = None
            for selector in delete_selectors:
                try:
                    potential_button = parent_container.find_element(By.XPATH, selector)
                    if potential_button:
                        # Additional validation: check if button contains hamburger menu
                        try:
                            hamburger_paths = potential_button.find_elements(By.XPATH, f".//path[@d='{hamburger_menu_path}']")
                            if hamburger_paths:
                                self._update_progress("⚠️ Skipping hamburger menu button")
                                continue  # Skip this button, it's a hamburger menu
                        except:
                            pass  # If check fails, proceed anyway

                        delete_button = potential_button
                        break
                except NoSuchElementException:
                    continue

            if not delete_button:
                # Try to find any button in the container (might be a menu button)
                buttons = parent_container.find_elements(By.TAG_NAME, "button")
                if buttons:
                    # Click the first button (likely a menu)
                    buttons[0].click()
                    time.sleep(1)

                    # Now look for delete option in the menu
                    delete_button = self.driver.find_element(By.XPATH, "//div[contains(text(), 'Delete') or contains(text(), 'Remove')]")

            if delete_button:
                # Click delete button
                self.driver.execute_script("arguments[0].click();", delete_button)
                time.sleep(1)

                # Look for confirmation dialog and confirm
                try:
                    confirm_selectors = [
                        "//button[contains(text(), 'Delete')]",
                        "//button[contains(text(), 'Remove')]",
                        "//button[contains(text(), 'Confirm')]",
                        "//div[@role='button' and contains(text(), 'Delete')]"
                    ]

                    for selector in confirm_selectors:
                        try:
                            confirm_button = WebDriverWait(self.driver, 3).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                            confirm_button.click()
                            break
                        except TimeoutException:
                            continue

                    self.deleted_count += 1
                    self._update_progress(f"Deleted comment #{self.deleted_count}")

                except TimeoutException:
                    # No confirmation needed, deletion was immediate
                    self.deleted_count += 1
                    self._update_progress(f"Deleted comment #{self.deleted_count}")
            else:
                self.logger.warning("Could not find delete button for this comment")

        except Exception as e:
            self.logger.warning(f"Error deleting comment: {str(e)}")

    def _scroll_to_load_more(self):
        """Scroll down to load more activities"""
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)

        # Also try clicking "Load more" if it exists
        try:
            load_more_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Load more') or contains(text(), 'Show more')]")
            if load_more_button.is_displayed():
                load_more_button.click()
                time.sleep(2)
        except NoSuchElementException:
            pass

    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)

    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
