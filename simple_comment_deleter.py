import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

class SimpleCommentDeleter:
    def __init__(self, progress_callback=None):
        self.driver = None
        self.progress_callback = progress_callback
        self.deleted_count = 0
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = ChromeOptions()
            if headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # Get ChromeDriver
            driver_path = ChromeDriverManager().install()
            service = ChromeService(driver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self._update_progress("Browser started successfully!")
            return True
            
        except Exception as e:
            self._update_progress(f"Failed to start browser: {str(e)}")
            return False
    
    def go_to_activity_page(self):
        """Navigate directly to YouTube activity page"""
        try:
            url = "https://myactivity.google.com/product/youtube?hl=en&utm_medium=web&utm_source=youtube"
            self._update_progress("Navigating to Google My Activity for YouTube...")
            
            self.driver.get(url)
            self.driver.maximize_window()
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            self._update_progress("Activity page loaded. Please sign in if needed.")
            return True
            
        except Exception as e:
            self._update_progress(f"Failed to navigate to activity page: {str(e)}")
            return False
    
    def wait_for_login(self):
        """Wait for user to complete login"""
        self._update_progress("Waiting for login... Please sign in to your Google account.")
        
        # Wait for the activity page to be fully loaded (indicates successful login)
        try:
            # Look for activity items or the main content area
            WebDriverWait(self.driver, 300).until(
                lambda driver: self._is_logged_in()
            )
            self._update_progress("Login successful! Ready to delete comments.")
            return True
        except TimeoutException:
            self._update_progress("Login timeout. Please try again.")
            return False
    
    def _is_logged_in(self):
        """Check if user is logged in by looking for activity content"""
        try:
            # Look for the main activity content or any activity items
            activity_indicators = [
                "//div[@data-value='YouTube']",  # YouTube filter
                "//div[contains(@class, 'activity')]",  # Activity items
                "//c-wiz",  # Main content wrapper
                "//div[contains(text(), 'YouTube')]"  # Any YouTube text
            ]
            
            for selector in activity_indicators:
                elements = self.driver.find_elements(By.XPATH, selector)
                if elements:
                    return True
            return False
        except:
            return False
    
    def find_and_delete_comments(self):
        """Find and delete YouTube comments from activity page"""
        try:
            self._update_progress("Starting comment deletion process...")
            
            while True:
                # Find comment activities - look for various comment indicators
                comment_selectors = [
                    "//div[contains(text(), 'Commented on')]",
                    "//div[contains(text(), 'comment')]",
                    "//div[contains(text(), 'Comment')]",
                    "//span[contains(text(), 'Commented on')]",
                    "//span[contains(text(), 'comment')]"
                ]
                
                comment_elements = []
                for selector in comment_selectors:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    comment_elements.extend(elements)
                
                if not comment_elements:
                    self._update_progress("No more comments found to delete.")
                    break
                
                self._update_progress(f"Found {len(comment_elements)} comment activities")
                
                # Process each comment
                for i, comment_element in enumerate(comment_elements[:5]):  # Process in batches
                    try:
                        self._delete_single_comment(comment_element)
                        time.sleep(2)  # Rate limiting
                    except Exception as e:
                        self.logger.warning(f"Failed to delete comment {i+1}: {str(e)}")
                        continue
                
                # Scroll down to load more activities
                self._scroll_to_load_more()
                time.sleep(3)
            
            self._update_progress(f"Deletion completed! Total comments deleted: {self.deleted_count}")
            
        except Exception as e:
            self._update_progress(f"Error during deletion process: {str(e)}")
    
    def _delete_single_comment(self, comment_element):
        """Delete a single comment activity"""
        try:
            # Scroll to the comment element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", comment_element)
            time.sleep(1)
            
            # Find the parent container that holds the delete button
            parent_container = comment_element.find_element(By.XPATH, "./ancestor::div[contains(@class, 'activity') or contains(@role, 'listitem') or @data-ved][1]")
            
            # Look for delete button - try multiple selectors
            delete_selectors = [
                ".//button[@aria-label='Delete']",
                ".//button[contains(@aria-label, 'Delete')]",
                ".//button[contains(@aria-label, 'Remove')]",
                ".//div[@role='button' and contains(@aria-label, 'Delete')]",
                ".//div[@role='button' and contains(@aria-label, 'Remove')]",
                ".//button[contains(text(), 'Delete')]",
                ".//span[contains(text(), 'Delete')]/ancestor::button[1]"
            ]
            
            delete_button = None
            for selector in delete_selectors:
                try:
                    delete_button = parent_container.find_element(By.XPATH, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not delete_button:
                # Try to find any button in the container (might be a menu button)
                buttons = parent_container.find_elements(By.TAG_NAME, "button")
                if buttons:
                    # Click the first button (likely a menu)
                    buttons[0].click()
                    time.sleep(1)
                    
                    # Now look for delete option in the menu
                    delete_button = self.driver.find_element(By.XPATH, "//div[contains(text(), 'Delete') or contains(text(), 'Remove')]")
            
            if delete_button:
                # Click delete button
                self.driver.execute_script("arguments[0].click();", delete_button)
                time.sleep(1)
                
                # Look for confirmation dialog and confirm
                try:
                    confirm_selectors = [
                        "//button[contains(text(), 'Delete')]",
                        "//button[contains(text(), 'Remove')]",
                        "//button[contains(text(), 'Confirm')]",
                        "//div[@role='button' and contains(text(), 'Delete')]"
                    ]
                    
                    for selector in confirm_selectors:
                        try:
                            confirm_button = WebDriverWait(self.driver, 3).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                            confirm_button.click()
                            break
                        except TimeoutException:
                            continue
                    
                    self.deleted_count += 1
                    self._update_progress(f"Deleted comment #{self.deleted_count}")
                    
                except TimeoutException:
                    # No confirmation needed, deletion was immediate
                    self.deleted_count += 1
                    self._update_progress(f"Deleted comment #{self.deleted_count}")
            else:
                self.logger.warning("Could not find delete button for this comment")
                
        except Exception as e:
            self.logger.warning(f"Error deleting comment: {str(e)}")
    
    def _scroll_to_load_more(self):
        """Scroll down to load more activities"""
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        
        # Also try clicking "Load more" if it exists
        try:
            load_more_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Load more') or contains(text(), 'Show more')]")
            if load_more_button.is_displayed():
                load_more_button.click()
                time.sleep(2)
        except NoSuchElementException:
            pass
    
    def _update_progress(self, message):
        """Update progress via callback"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message)
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            self.driver = None
